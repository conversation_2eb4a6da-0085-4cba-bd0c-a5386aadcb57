import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import '../services/audio_service.dart';
import '../services/vibration_service.dart';
import '../services/score_service.dart';
import 'results_screen.dart';

class QuizScreen extends StatefulWidget {
  const QuizScreen({super.key});

  @override
  State<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends State<QuizScreen> {
  int _timeLeft = 30;
  Timer? _timer;
  String? _selectedAnswer;
  bool _answered = false;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timeLeft = 30;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_timeLeft > 0) {
          _timeLeft--;
        } else {
          _timer?.cancel();
          _handleTimeout();
        }
      });
    });
  }

  void _handleTimeout() {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    final audioService = AudioService();
    final vibrationService = VibrationService();

    // If not answered, mark as incorrect
    if (!_answered) {
      audioService.playIncorrectSound();
      vibrationService.vibrateOnAnswer(false);
      quizProvider.answerQuestion('');
      _moveToNextQuestion();
    }
  }

  void _moveToNextQuestion() {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    final audioService = AudioService();

    // Reset state for next question
    setState(() {
      _selectedAnswer = null;
      _answered = false;
    });

    // Delay before moving to next question
    Future.delayed(const Duration(seconds: 1), () {
      if (quizProvider.currentQuestionIndex <
          quizProvider.questions.length - 1) {
        quizProvider.nextQuestion();
        _startTimer();
        audioService.playButtonSound(); // Sound for new question
      } else {
        // Save high score and navigate to results screen
        quizProvider.saveHighScore(quizProvider.score);
        audioService.playSuccessSound(); // Sound for completing quiz

        // Also save to score service for leaderboard
        final scoreService = ScoreService();
        scoreService.addScore(
          ScoreEntry(
            category: quizProvider.questions[0].category,
            difficulty: quizProvider.questions[0].difficulty,
            score: quizProvider.score,
            totalQuestions: quizProvider.questions.length,
            date: DateTime.now(),
          ),
        );

        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const ResultsScreen()),
          );
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final quizProvider = Provider.of<QuizProvider>(context);
    final l10n = AppLocalizations.of(context);
    final currentQuestion =
        quizProvider.questions[quizProvider.currentQuestionIndex];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${l10n.question} ${quizProvider.currentQuestionIndex + 1}/${quizProvider.questions.length}',
        ),
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Timer and progress
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Timer
                Row(
                  children: [
                    const Icon(Icons.timer),
                    const SizedBox(width: 8),
                    Text(
                      '$_timeLeft ${l10n.seconds}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _timeLeft < 10 ? Colors.red : Colors.black,
                      ),
                    ),
                  ],
                ),
                // Score
                Text(
                  '${l10n.score}: ${quizProvider.score}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            // Progress indicator
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value:
                  (quizProvider.currentQuestionIndex + 1) /
                  quizProvider.questions.length,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            ),

            // Category and difficulty
            const SizedBox(height: 16),
            Row(
              children: [
                Chip(
                  label: Text(currentQuestion.displayCategory),
                  backgroundColor: Colors.blue.shade100,
                ),
                const SizedBox(width: 8),
                Chip(
                  label: Text(
                    currentQuestion.difficulty.toUpperCase(),
                    style: TextStyle(
                      color:
                          currentQuestion.difficulty == 'easy'
                              ? Colors.green
                              : currentQuestion.difficulty == 'medium'
                              ? Colors.orange
                              : Colors.red,
                    ),
                  ),
                  backgroundColor: Colors.grey.shade200,
                ),
              ],
            ),

            // Question
            const SizedBox(height: 24),
            Text(
              currentQuestion.displayQuestion,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),

            // Answer options
            const SizedBox(height: 24),
            Expanded(
              child: ListView.builder(
                itemCount: currentQuestion.displayAllAnswers.length,
                itemBuilder: (context, index) {
                  final answer = currentQuestion.displayAllAnswers[index];

                  // Determine button color based on selection and correctness
                  Color? buttonColor;
                  if (_answered) {
                    if (answer == currentQuestion.displayCorrectAnswer) {
                      buttonColor = Colors.green;
                    } else if (answer == _selectedAnswer) {
                      buttonColor = Colors.red;
                    }
                  } else if (answer == _selectedAnswer) {
                    buttonColor = Colors.blue;
                  }

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12.0),
                    child: ElevatedButton(
                      onPressed:
                          _answered
                              ? null
                              : () {
                                final audioService = AudioService();
                                final vibrationService = VibrationService();
                                final isCorrect =
                                    answer ==
                                    currentQuestion.displayCorrectAnswer;

                                // Play sound and vibrate based on correctness
                                if (isCorrect) {
                                  audioService.playCorrectSound();
                                  vibrationService.vibrateOnAnswer(true);
                                } else {
                                  audioService.playIncorrectSound();
                                  vibrationService.vibrateOnAnswer(false);
                                }

                                setState(() {
                                  _selectedAnswer = answer;
                                  _answered = true;
                                });
                                _timer?.cancel();
                                quizProvider.answerQuestion(answer);
                                _moveToNextQuestion();
                              },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonColor,
                        foregroundColor:
                            buttonColor != null ? Colors.white : null,
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                        alignment: Alignment.centerLeft,
                      ),
                      child: Text(answer, style: const TextStyle(fontSize: 16)),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
