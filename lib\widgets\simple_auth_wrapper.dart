import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/simple_auth_service.dart';
import '../screens/auth/login_screen.dart';
import '../widgets/main_navigation.dart';

class SimpleAuthWrapper extends StatefulWidget {
  const SimpleAuthWrapper({super.key});

  @override
  State<SimpleAuthWrapper> createState() => _SimpleAuthWrapperState();
}

class _SimpleAuthWrapperState extends State<SimpleAuthWrapper> {
  @override
  void initState() {
    super.initState();
    // Charger l'état d'authentification au démarrage
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authService = Provider.of<SimpleAuthService>(context, listen: false);
      authService.loadAuthState();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SimpleAuthService>(
      builder: (context, authService, _) {
        // Si l'utilisateur est connecté, afficher l'app principale
        if (authService.isAuthenticated) {
          return const MainNavigation();
        }
        
        // Sinon, afficher l'écran de connexion
        return const LoginScreen();
      },
    );
  }
}
