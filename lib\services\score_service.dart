import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'auth_service.dart';

class ScoreEntry {
  final String category;
  final String difficulty;
  final int score;
  final int totalQuestions;
  final DateTime date;

  ScoreEntry({
    required this.category,
    required this.difficulty,
    required this.score,
    required this.totalQuestions,
    required this.date,
  });

  double get percentage => (score / totalQuestions) * 100;

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'difficulty': difficulty,
      'score': score,
      'totalQuestions': totalQuestions,
      'date': date.toIso8601String(),
    };
  }

  factory ScoreEntry.fromJson(Map<String, dynamic> json) {
    return ScoreEntry(
      category: json['category'],
      difficulty: json['difficulty'],
      score: json['score'],
      totalQuestions: json['totalQuestions'],
      date: DateTime.parse(json['date']),
    );
  }
}

class ScoreService extends ChangeNotifier {
  static final ScoreService _instance = ScoreService._internal();
  factory ScoreService() => _instance;
  ScoreService._internal();

  List<ScoreEntry> _scores = [];
  List<ScoreEntry> get scores => _scores;
  AuthService? _authService;

  Future<void> initialize() async {
    await _loadScores();
  }

  void setAuthService(AuthService authService) {
    _authService = authService;
  }

  Future<void> _loadScores() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final scoresJson = prefs.getStringList('scores') ?? [];

      _scores =
          scoresJson
              .map((json) => ScoreEntry.fromJson(jsonDecode(json)))
              .toList();

      // Sort by date (newest first)
      _scores.sort((a, b) => b.date.compareTo(a.date));
    } catch (e) {
      debugPrint('Error loading scores: $e');
      _scores = [];
    }
  }

  Future<void> _saveScores() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final scoresJson =
          _scores.map((score) => jsonEncode(score.toJson())).toList();

      await prefs.setStringList('scores', scoresJson);
    } catch (e) {
      debugPrint('Error saving scores: $e');
    }
  }

  Future<void> addScore(ScoreEntry score) async {
    _scores.add(score);

    // Sort by date (newest first)
    _scores.sort((a, b) => b.date.compareTo(a.date));

    await _saveScores();

    // Si l'utilisateur est connecté, sauvegarder aussi dans Firebase
    if (_authService?.isAuthenticated == true) {
      try {
        await _authService!.updateUserStats(
          score: score.score,
          totalQuestions: score.totalQuestions,
          category: score.category,
          difficulty: score.difficulty,
        );
      } catch (e) {
        debugPrint('Error saving score to Firebase: $e');
      }
    }

    notifyListeners();
  }

  Future<void> resetScores() async {
    _scores = [];
    await _saveScores();
    notifyListeners();
  }

  List<ScoreEntry> getScoresByCategory(String category) {
    return _scores.where((score) => score.category == category).toList();
  }

  List<ScoreEntry> getScoresByDifficulty(String difficulty) {
    return _scores.where((score) => score.difficulty == difficulty).toList();
  }

  List<ScoreEntry> getScoresByCategoryAndDifficulty(
    String category,
    String difficulty,
  ) {
    return _scores
        .where(
          (score) =>
              score.category == category && score.difficulty == difficulty,
        )
        .toList();
  }

  ScoreEntry? getBestScore() {
    if (_scores.isEmpty) return null;

    return _scores.reduce((a, b) => a.percentage > b.percentage ? a : b);
  }

  ScoreEntry? getBestScoreByCategory(String category) {
    final categoryScores = getScoresByCategory(category);
    if (categoryScores.isEmpty) return null;

    return categoryScores.reduce((a, b) => a.percentage > b.percentage ? a : b);
  }

  ScoreEntry? getBestScoreByDifficulty(String difficulty) {
    final difficultyScores = getScoresByDifficulty(difficulty);
    if (difficultyScores.isEmpty) return null;

    return difficultyScores.reduce(
      (a, b) => a.percentage > b.percentage ? a : b,
    );
  }
}
