import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Arrière-plan professionnel avec éléments de quiz animés
class ProfessionalQuizBackground extends StatefulWidget {
  final Widget child;
  final bool animated;
  final Color? primaryColor;
  final Color? secondaryColor;

  const ProfessionalQuizBackground({
    super.key,
    required this.child,
    this.animated = true,
    this.primaryColor,
    this.secondaryColor,
  });

  @override
  State<ProfessionalQuizBackground> createState() => _ProfessionalQuizBackgroundState();
}

class _ProfessionalQuizBackgroundState extends State<ProfessionalQuizBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _floatingController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _floatingAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _floatingAnimation = Tween<double>(
      begin: -10,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    if (widget.animated) {
      _controller.repeat();
      _floatingController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _floatingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = widget.primaryColor ?? Theme.of(context).colorScheme.primary;
    final secondaryColor = widget.secondaryColor ?? Theme.of(context).colorScheme.secondary;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            primaryColor,
            primaryColor.withValues(alpha: 0.8),
            secondaryColor.withValues(alpha: 0.9),
            secondaryColor,
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: Stack(
        children: [
          // Motifs géométriques de fond
          _buildGeometricPattern(context),
          
          // Éléments de quiz flottants
          if (widget.animated) _buildFloatingQuizElements(context),
          
          // Overlay avec transparence
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.1),
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.05),
                ],
              ),
            ),
          ),
          
          // Contenu principal
          widget.child,
        ],
      ),
    );
  }

  Widget _buildGeometricPattern(BuildContext context) {
    return CustomPaint(
      size: Size.infinite,
      painter: GeometricPatternPainter(
        animation: _rotationAnimation,
        primaryColor: widget.primaryColor ?? Theme.of(context).colorScheme.primary,
        secondaryColor: widget.secondaryColor ?? Theme.of(context).colorScheme.secondary,
      ),
    );
  }

  Widget _buildFloatingQuizElements(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationAnimation, _floatingAnimation]),
      builder: (context, child) {
        return Stack(
          children: [
            // Question mark flottant
            Positioned(
              top: 100 + _floatingAnimation.value,
              right: 50,
              child: Transform.rotate(
                angle: _rotationAnimation.value * 0.5,
                child: Icon(
                  Icons.help_outline,
                  size: 40,
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
            ),
            
            // Lightbulb flottant
            Positioned(
              top: 200 + _floatingAnimation.value * -1,
              left: 30,
              child: Transform.rotate(
                angle: _rotationAnimation.value * -0.3,
                child: Icon(
                  Icons.lightbulb_outline,
                  size: 35,
                  color: Colors.white.withValues(alpha: 0.08),
                ),
              ),
            ),
            
            // Quiz icon flottant
            Positioned(
              bottom: 150 + _floatingAnimation.value,
              right: 80,
              child: Transform.rotate(
                angle: _rotationAnimation.value * 0.4,
                child: Icon(
                  Icons.quiz_outlined,
                  size: 45,
                  color: Colors.white.withValues(alpha: 0.12),
                ),
              ),
            ),
            
            // Brain icon flottant
            Positioned(
              bottom: 250 + _floatingAnimation.value * -1,
              left: 60,
              child: Transform.rotate(
                angle: _rotationAnimation.value * -0.2,
                child: Icon(
                  Icons.psychology_outlined,
                  size: 38,
                  color: Colors.white.withValues(alpha: 0.09),
                ),
              ),
            ),
            
            // Star flottant
            Positioned(
              top: 300 + _floatingAnimation.value * 0.5,
              left: MediaQuery.of(context).size.width * 0.7,
              child: Transform.rotate(
                angle: _rotationAnimation.value * 0.6,
                child: Icon(
                  Icons.star_outline,
                  size: 30,
                  color: Colors.white.withValues(alpha: 0.07),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

/// Painter pour les motifs géométriques de fond
class GeometricPatternPainter extends CustomPainter {
  final Animation<double> animation;
  final Color primaryColor;
  final Color secondaryColor;

  GeometricPatternPainter({
    required this.animation,
    required this.primaryColor,
    required this.secondaryColor,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    // Cercles concentriques
    _drawConcentricCircles(canvas, size, paint);
    
    // Lignes diagonales
    _drawDiagonalLines(canvas, size, paint);
    
    // Hexagones
    _drawHexagons(canvas, size, paint);
  }

  void _drawConcentricCircles(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width * 0.2, size.height * 0.8);
    paint.color = Colors.white.withValues(alpha: 0.05);
    
    for (int i = 1; i <= 4; i++) {
      final radius = 50.0 * i + (animation.value * 20);
      canvas.drawCircle(center, radius, paint);
    }
  }

  void _drawDiagonalLines(Canvas canvas, Size size, Paint paint) {
    paint.color = Colors.white.withValues(alpha: 0.03);
    
    for (int i = 0; i < 8; i++) {
      final x = (size.width / 8) * i + (animation.value * 50);
      canvas.drawLine(
        Offset(x, 0),
        Offset(x + size.height * 0.3, size.height),
        paint,
      );
    }
  }

  void _drawHexagons(Canvas canvas, Size size, Paint paint) {
    paint.color = Colors.white.withValues(alpha: 0.04);
    
    final center = Offset(size.width * 0.8, size.height * 0.3);
    final radius = 60.0;
    
    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi / 3) + animation.value;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);
      
      _drawHexagon(canvas, Offset(x, y), 25, paint);
    }
  }

  void _drawHexagon(Canvas canvas, Offset center, double radius, Paint paint) {
    final path = Path();
    for (int i = 0; i < 6; i++) {
      final angle = i * math.pi / 3;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Widget pour créer des cartes avec effet professionnel
class ProfessionalCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final Color? backgroundColor;
  final List<BoxShadow>? boxShadow;
  final VoidCallback? onTap;

  const ProfessionalCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius = 20,
    this.backgroundColor,
    this.boxShadow,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding ?? const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
              boxShadow: boxShadow ?? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}
