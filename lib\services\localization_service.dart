import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalizationService extends ChangeNotifier {
  static final LocalizationService _instance = LocalizationService._internal();
  factory LocalizationService() => _instance;
  LocalizationService._internal();

  Locale _locale = const Locale('fr');
  Locale get locale => _locale;

  bool _isChangingLanguage = false;
  bool get isChangingLanguage => _isChangingLanguage;

  final List<Locale> supportedLocales = [
    const Locale('fr'),
    const Locale('en'),
    const Locale('ar'),
  ];

  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('language_code');
    if (languageCode != null) {
      _locale = Locale(languageCode);
      notifyListeners();
    }
  }

  Future<void> setLocale(Locale locale) async {
    if (!supportedLocales.contains(locale)) return;

    if (_locale.languageCode == locale.languageCode) {
      return; // Éviter les changements inutiles
    }

    _isChangingLanguage = true;
    notifyListeners();

    // Petit délai pour l'animation
    await Future.delayed(const Duration(milliseconds: 150));

    _locale = locale;

    // Sauvegarder immédiatement
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', locale.languageCode);

    _isChangingLanguage = false;
    // Notifier les listeners après la sauvegarde
    notifyListeners();
  }

  String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return 'Français';
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      default:
        return 'Français';
    }
  }

  String getLanguageNativeName(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return 'Français';
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      default:
        return 'Français';
    }
  }

  String getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'ar':
        return '🇸🇦';
      default:
        return '🌐';
    }
  }

  // Vérifier si la langue actuelle est RTL
  bool get isRTL => _locale.languageCode == 'ar';

  // Obtenir la direction du texte
  TextDirection get textDirection =>
      isRTL ? TextDirection.rtl : TextDirection.ltr;

  // Obtenir l'alignement du texte selon la direction
  TextAlign get textAlign => isRTL ? TextAlign.right : TextAlign.left;

  // Obtenir l'alignement opposé
  TextAlign get oppositeTextAlign => isRTL ? TextAlign.left : TextAlign.right;

  // Obtenir l'alignement pour les titres (centré généralement)
  TextAlign get titleTextAlign => TextAlign.center;
}
