import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TranslationService {
  static final TranslationService _instance = TranslationService._internal();
  factory TranslationService() => _instance;
  TranslationService._internal();

  // Cache pour éviter les traductions répétées
  final Map<String, Map<String, String>> _translationCache = {};

  // Mapping des catégories en anglais vers français et arabe
  final Map<String, Map<String, String>> _categoryTranslations = {
    'General Knowledge': {
      'fr': 'Culture Générale',
      'ar': 'المعرفة العامة',
      'en': 'General Knowledge',
    },
    'Entertainment: Books': {
      'fr': 'Divertissement : Livres',
      'ar': 'الترفيه: الكتب',
      'en': 'Entertainment: Books',
    },
    'Entertainment: Film': {
      'fr': 'Divertissement : Cinéma',
      'ar': 'الترفيه: الأفلام',
      'en': 'Entertainment: Film',
    },
    'Entertainment: Music': {
      'fr': 'Divertissement : Musique',
      'ar': 'الترفيه: الموسيقى',
      'en': 'Entertainment: Music',
    },
    'Entertainment: Musicals & Theatres': {
      'fr': 'Divertissement : Comédies musicales et théâtres',
      'ar': 'الترفيه: المسرحيات الموسيقية والمسارح',
      'en': 'Entertainment: Musicals & Theatres',
    },
    'Entertainment: Television': {
      'fr': 'Divertissement : Télévision',
      'ar': 'الترفيه: التلفزيون',
      'en': 'Entertainment: Television',
    },
    'Entertainment: Video Games': {
      'fr': 'Divertissement : Jeux vidéo',
      'ar': 'الترفيه: ألعاب الفيديو',
      'en': 'Entertainment: Video Games',
    },
    'Entertainment: Board Games': {
      'fr': 'Divertissement : Jeux de société',
      'ar': 'الترفيه: ألعاب الطاولة',
      'en': 'Entertainment: Board Games',
    },
    'Science & Nature': {
      'fr': 'Sciences et Nature',
      'ar': 'العلوم والطبيعة',
      'en': 'Science & Nature',
    },
    'Science: Computers': {
      'fr': 'Sciences : Informatique',
      'ar': 'العلوم: الحاسوب',
      'en': 'Science: Computers',
    },
    'Science: Mathematics': {
      'fr': 'Sciences : Mathématiques',
      'ar': 'العلوم: الرياضيات',
      'en': 'Science: Mathematics',
    },
    'Mythology': {
      'fr': 'Mythologie',
      'ar': 'الأساطير',
      'en': 'Mythology',
    },
    'Sports': {
      'fr': 'Sports',
      'ar': 'الرياضة',
      'en': 'Sports',
    },
    'Geography': {
      'fr': 'Géographie',
      'ar': 'الجغرافيا',
      'en': 'Geography',
    },
    'History': {
      'fr': 'Histoire',
      'ar': 'التاريخ',
      'en': 'History',
    },
    'Politics': {
      'fr': 'Politique',
      'ar': 'السياسة',
      'en': 'Politics',
    },
    'Art': {
      'fr': 'Art',
      'ar': 'الفن',
      'en': 'Art',
    },
    'Celebrities': {
      'fr': 'Célébrités',
      'ar': 'المشاهير',
      'en': 'Celebrities',
    },
    'Animals': {
      'fr': 'Animaux',
      'ar': 'الحيوانات',
      'en': 'Animals',
    },
    'Vehicles': {
      'fr': 'Véhicules',
      'ar': 'المركبات',
      'en': 'Vehicles',
    },
    'Entertainment: Comics': {
      'fr': 'Divertissement : Bandes dessinées',
      'ar': 'الترفيه: القصص المصورة',
      'en': 'Entertainment: Comics',
    },
    'Science: Gadgets': {
      'fr': 'Sciences : Gadgets',
      'ar': 'العلوم: الأجهزة',
      'en': 'Science: Gadgets',
    },
    'Entertainment: Japanese Anime & Manga': {
      'fr': 'Divertissement : Anime et Manga japonais',
      'ar': 'الترفيه: الأنمي والمانجا اليابانية',
      'en': 'Entertainment: Japanese Anime & Manga',
    },
    'Entertainment: Cartoon & Animations': {
      'fr': 'Divertissement : Dessins animés',
      'ar': 'الترفيه: الرسوم المتحركة',
      'en': 'Entertainment: Cartoon & Animations',
    },
  };

  // Mapping des difficultés
  final Map<String, Map<String, String>> _difficultyTranslations = {
    'easy': {
      'fr': 'Facile',
      'ar': 'سهل',
      'en': 'Easy',
    },
    'medium': {
      'fr': 'Moyen',
      'ar': 'متوسط',
      'en': 'Medium',
    },
    'hard': {
      'fr': 'Difficile',
      'ar': 'صعب',
      'en': 'Hard',
    },
  };

  /// Traduit le nom d'une catégorie
  String translateCategory(String categoryName, String languageCode) {
    if (_categoryTranslations.containsKey(categoryName)) {
      return _categoryTranslations[categoryName]![languageCode] ?? categoryName;
    }
    return categoryName;
  }

  /// Traduit le nom d'une difficulté
  String translateDifficulty(String difficulty, String languageCode) {
    if (_difficultyTranslations.containsKey(difficulty)) {
      return _difficultyTranslations[difficulty]![languageCode] ?? difficulty;
    }
    return difficulty;
  }

  /// Traduit un texte en utilisant un service de traduction (simulation)
  Future<String> translateText(String text, String targetLanguage) async {
    // Clé de cache
    final cacheKey = '${text}_$targetLanguage';
    
    // Vérifier le cache
    if (_translationCache.containsKey(targetLanguage) && 
        _translationCache[targetLanguage]!.containsKey(text)) {
      return _translationCache[targetLanguage]![text]!;
    }

    // Pour cette démo, nous utilisons des traductions prédéfinies
    // Dans un vrai projet, vous utiliseriez Google Translate API ou similaire
    String translatedText = await _getTranslationFromCache(text, targetLanguage);
    
    // Mettre en cache
    _translationCache[targetLanguage] ??= {};
    _translationCache[targetLanguage]![text] = translatedText;
    
    return translatedText;
  }

  /// Simulation d'un service de traduction avec des traductions prédéfinies
  Future<String> _getTranslationFromCache(String text, String targetLanguage) async {
    // Simulation d'un délai réseau
    await Future.delayed(const Duration(milliseconds: 100));
    
    // Traductions prédéfinies pour les textes courants
    final predefinedTranslations = {
      'fr': {
        'True': 'Vrai',
        'False': 'Faux',
        'Yes': 'Oui',
        'No': 'Non',
        'What': 'Quel',
        'Which': 'Lequel',
        'Who': 'Qui',
        'When': 'Quand',
        'Where': 'Où',
        'How': 'Comment',
        'Why': 'Pourquoi',
      },
      'ar': {
        'True': 'صحيح',
        'False': 'خطأ',
        'Yes': 'نعم',
        'No': 'لا',
        'What': 'ما',
        'Which': 'أي',
        'Who': 'من',
        'When': 'متى',
        'Where': 'أين',
        'How': 'كيف',
        'Why': 'لماذا',
      },
    };

    if (targetLanguage == 'en') {
      return text; // Pas de traduction nécessaire pour l'anglais
    }

    if (predefinedTranslations.containsKey(targetLanguage) &&
        predefinedTranslations[targetLanguage]!.containsKey(text)) {
      return predefinedTranslations[targetLanguage]![text]!;
    }

    // Si pas de traduction prédéfinie, retourner le texte original
    return text;
  }

  /// Traduit une liste de réponses
  Future<List<String>> translateAnswers(List<String> answers, String targetLanguage) async {
    if (targetLanguage == 'en') return answers;
    
    List<String> translatedAnswers = [];
    for (String answer in answers) {
      String translated = await translateText(answer, targetLanguage);
      translatedAnswers.add(translated);
    }
    return translatedAnswers;
  }

  /// Traduit une question
  Future<String> translateQuestion(String question, String targetLanguage) async {
    if (targetLanguage == 'en') return question;
    return await translateText(question, targetLanguage);
  }

  /// Nettoie le cache de traduction
  void clearCache() {
    _translationCache.clear();
  }
}
