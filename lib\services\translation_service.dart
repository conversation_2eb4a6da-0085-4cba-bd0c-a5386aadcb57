import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TranslationService {
  static final TranslationService _instance = TranslationService._internal();
  factory TranslationService() => _instance;
  TranslationService._internal();

  // Cache pour éviter les traductions répétées
  final Map<String, Map<String, String>> _translationCache = {};

  // Mapping des catégories en anglais vers français et arabe
  final Map<String, Map<String, String>> _categoryTranslations = {
    'General Knowledge': {
      'fr': 'Culture Générale',
      'ar': 'المعرفة العامة',
      'en': 'General Knowledge',
    },
    'Entertainment: Books': {
      'fr': 'Divertissement : Livres',
      'ar': 'الترفيه: الكتب',
      'en': 'Entertainment: Books',
    },
    'Entertainment: Film': {
      'fr': 'Divertissement : Cinéma',
      'ar': 'الترفيه: الأفلام',
      'en': 'Entertainment: Film',
    },
    'Entertainment: Music': {
      'fr': 'Divertissement : Musique',
      'ar': 'الترفيه: الموسيقى',
      'en': 'Entertainment: Music',
    },
    'Entertainment: Musicals & Theatres': {
      'fr': 'Divertissement : Comédies musicales et théâtres',
      'ar': 'الترفيه: المسرحيات الموسيقية والمسارح',
      'en': 'Entertainment: Musicals & Theatres',
    },
    'Entertainment: Television': {
      'fr': 'Divertissement : Télévision',
      'ar': 'الترفيه: التلفزيون',
      'en': 'Entertainment: Television',
    },
    'Entertainment: Video Games': {
      'fr': 'Divertissement : Jeux vidéo',
      'ar': 'الترفيه: ألعاب الفيديو',
      'en': 'Entertainment: Video Games',
    },
    'Entertainment: Board Games': {
      'fr': 'Divertissement : Jeux de société',
      'ar': 'الترفيه: ألعاب الطاولة',
      'en': 'Entertainment: Board Games',
    },
    'Science & Nature': {
      'fr': 'Sciences et Nature',
      'ar': 'العلوم والطبيعة',
      'en': 'Science & Nature',
    },
    'Science: Computers': {
      'fr': 'Sciences : Informatique',
      'ar': 'العلوم: الحاسوب',
      'en': 'Science: Computers',
    },
    'Science: Mathematics': {
      'fr': 'Sciences : Mathématiques',
      'ar': 'العلوم: الرياضيات',
      'en': 'Science: Mathematics',
    },
    'Mythology': {'fr': 'Mythologie', 'ar': 'الأساطير', 'en': 'Mythology'},
    'Sports': {'fr': 'Sports', 'ar': 'الرياضة', 'en': 'Sports'},
    'Geography': {'fr': 'Géographie', 'ar': 'الجغرافيا', 'en': 'Geography'},
    'History': {'fr': 'Histoire', 'ar': 'التاريخ', 'en': 'History'},
    'Politics': {'fr': 'Politique', 'ar': 'السياسة', 'en': 'Politics'},
    'Art': {'fr': 'Art', 'ar': 'الفن', 'en': 'Art'},
    'Celebrities': {'fr': 'Célébrités', 'ar': 'المشاهير', 'en': 'Celebrities'},
    'Animals': {'fr': 'Animaux', 'ar': 'الحيوانات', 'en': 'Animals'},
    'Vehicles': {'fr': 'Véhicules', 'ar': 'المركبات', 'en': 'Vehicles'},
    'Entertainment: Comics': {
      'fr': 'Divertissement : Bandes dessinées',
      'ar': 'الترفيه: القصص المصورة',
      'en': 'Entertainment: Comics',
    },
    'Science: Gadgets': {
      'fr': 'Sciences : Gadgets',
      'ar': 'العلوم: الأجهزة',
      'en': 'Science: Gadgets',
    },
    'Entertainment: Japanese Anime & Manga': {
      'fr': 'Divertissement : Anime et Manga japonais',
      'ar': 'الترفيه: الأنمي والمانجا اليابانية',
      'en': 'Entertainment: Japanese Anime & Manga',
    },
    'Entertainment: Cartoon & Animations': {
      'fr': 'Divertissement : Dessins animés',
      'ar': 'الترفيه: الرسوم المتحركة',
      'en': 'Entertainment: Cartoon & Animations',
    },
  };

  // Mapping des difficultés
  final Map<String, Map<String, String>> _difficultyTranslations = {
    'easy': {'fr': 'Facile', 'ar': 'سهل', 'en': 'Easy'},
    'medium': {'fr': 'Moyen', 'ar': 'متوسط', 'en': 'Medium'},
    'hard': {'fr': 'Difficile', 'ar': 'صعب', 'en': 'Hard'},
  };

  /// Traduit le nom d'une catégorie
  String translateCategory(String categoryName, String languageCode) {
    if (_categoryTranslations.containsKey(categoryName)) {
      return _categoryTranslations[categoryName]![languageCode] ?? categoryName;
    }
    return categoryName;
  }

  /// Traduit le nom d'une difficulté
  String translateDifficulty(String difficulty, String languageCode) {
    if (_difficultyTranslations.containsKey(difficulty)) {
      return _difficultyTranslations[difficulty]![languageCode] ?? difficulty;
    }
    return difficulty;
  }

  /// Traduit un texte en utilisant un service de traduction (simulation)
  Future<String> translateText(String text, String targetLanguage) async {
    // Vérifier le cache
    if (_translationCache.containsKey(targetLanguage) &&
        _translationCache[targetLanguage]!.containsKey(text)) {
      return _translationCache[targetLanguage]![text]!;
    }

    // Pour cette démo, nous utilisons des traductions prédéfinies
    // Dans un vrai projet, vous utiliseriez Google Translate API ou similaire
    String translatedText = await _getTranslationFromCache(
      text,
      targetLanguage,
    );

    // Mettre en cache
    _translationCache[targetLanguage] ??= {};
    _translationCache[targetLanguage]![text] = translatedText;

    return translatedText;
  }

  /// Service de traduction amélioré avec plus de traductions prédéfinies
  Future<String> _getTranslationFromCache(
    String text,
    String targetLanguage,
  ) async {
    // Simulation d'un délai réseau
    await Future.delayed(const Duration(milliseconds: 50));

    if (targetLanguage == 'en') {
      return text; // Pas de traduction nécessaire pour l'anglais
    }

    // Traductions prédéfinies étendues
    final translatedText = await _translateWithPredefinedRules(
      text,
      targetLanguage,
    );
    return translatedText;
  }

  /// Traduction avec règles prédéfinies étendues
  Future<String> _translateWithPredefinedRules(
    String text,
    String targetLanguage,
  ) async {
    final predefinedTranslations = {
      'fr': {
        // Réponses courantes
        'True': 'Vrai',
        'False': 'Faux',
        'Yes': 'Oui',
        'No': 'Non',
        'Evening': 'Soir',
        'Midnight': 'Minuit',
        'Morning': 'Matin',
        'Afternoon': 'Après-midi',
        'Night': 'Nuit',
        'Day': 'Jour',

        // Mots interrogatifs
        'What': 'Quel',
        'Which': 'Lequel',
        'Who': 'Qui',
        'When': 'Quand',
        'Where': 'Où',
        'How': 'Comment',
        'Why': 'Pourquoi',

        // Nombres et temps
        'One': 'Un',
        'Two': 'Deux',
        'Three': 'Trois',
        'Four': 'Quatre',
        'Five': 'Cinq',
        'Six': 'Six',
        'Seven': 'Sept',
        'Eight': 'Huit',
        'Nine': 'Neuf',
        'Ten': 'Dix',
        '6': '6',
        '15': '15',
        '20': '20',
        '25': '25',
        '30': '30',

        // Couleurs
        'Red': 'Rouge',
        'Blue': 'Bleu',
        'Green': 'Vert',
        'Yellow': 'Jaune',
        'Black': 'Noir',
        'White': 'Blanc',
        'Orange': 'Orange',
        'Purple': 'Violet',
        'Pink': 'Rose',
        'Brown': 'Marron',

        // Pays et lieux
        'France': 'France',
        'Germany': 'Allemagne',
        'Italy': 'Italie',
        'Spain': 'Espagne',
        'England': 'Angleterre',
        'United States': 'États-Unis',
        'Canada': 'Canada',
        'Japan': 'Japon',
        'China': 'Chine',
        'Russia': 'Russie',
        'Brazil': 'Brésil',
        'Australia': 'Australie',

        // Mots courants dans les questions
        'movie': 'film',
        'film': 'film',
        'book': 'livre',
        'song': 'chanson',
        'game': 'jeu',
        'character': 'personnage',
        'actor': 'acteur',
        'actress': 'actrice',
        'director': 'réalisateur',
        'author': 'auteur',
        'artist': 'artiste',
        'singer': 'chanteur',
        'band': 'groupe',
        'album': 'album',

        // Films et personnages spécifiques
        'Gremlins': 'Gremlins',
        'Mogwai': 'Mogwai',
        'Batman': 'Batman',
        'Superman': 'Superman',
        'Spider-Man': 'Spider-Man',
        'year': 'année',
        'century': 'siècle',
        'decade': 'décennie',
        'war': 'guerre',
        'battle': 'bataille',
        'king': 'roi',
        'queen': 'reine',
        'president': 'président',
        'country': 'pays',
        'city': 'ville',
        'capital': 'capitale',
        'language': 'langue',
        'currency': 'monnaie',
        'planet': 'planète',
        'animal': 'animal',
        'plant': 'plante',
        'element': 'élément',
        'chemical': 'chimique',
        'formula': 'formule',
        'theory': 'théorie',
        'scientist': 'scientifique',
        'invention': 'invention',
        'discovery': 'découverte',
        'sport': 'sport',
        'team': 'équipe',
        'player': 'joueur',
        'championship': 'championnat',
        'olympic': 'olympique',
        'medal': 'médaille',
        'gold': 'or',
        'silver': 'argent',
        'bronze': 'bronze',
      },
      'ar': {
        // Réponses courantes
        'True': 'صحيح',
        'False': 'خطأ',
        'Yes': 'نعم',
        'No': 'لا',
        'Evening': 'مساء',
        'Midnight': 'منتصف الليل',
        'Morning': 'صباح',
        'Afternoon': 'بعد الظهر',
        'Night': 'ليل',
        'Day': 'يوم',

        // Mots interrogatifs
        'What': 'ما',
        'Which': 'أي',
        'Who': 'من',
        'When': 'متى',
        'Where': 'أين',
        'How': 'كيف',
        'Why': 'لماذا',

        // Nombres
        'One': 'واحد',
        'Two': 'اثنان',
        'Three': 'ثلاثة',
        'Four': 'أربعة',
        'Five': 'خمسة',
        'Six': 'ستة',
        'Seven': 'سبعة',
        'Eight': 'ثمانية',
        'Nine': 'تسعة',
        'Ten': 'عشرة',

        // Couleurs
        'Red': 'أحمر',
        'Blue': 'أزرق',
        'Green': 'أخضر',
        'Yellow': 'أصفر',
        'Black': 'أسود',
        'White': 'أبيض',
        'Orange': 'برتقالي',
        'Purple': 'بنفسجي',
        'Pink': 'وردي',
        'Brown': 'بني',

        // Mots courants
        'movie': 'فيلم',
        'film': 'فيلم',
        'book': 'كتاب',
        'song': 'أغنية',
        'game': 'لعبة',
        'character': 'شخصية',
        'actor': 'ممثل',
        'actress': 'ممثلة',
        'director': 'مخرج',
        'author': 'مؤلف',
        'artist': 'فنان',
        'singer': 'مغني',
        'band': 'فرقة',
        'album': 'ألبوم',
        'year': 'سنة',
        'century': 'قرن',
        'war': 'حرب',
        'battle': 'معركة',
        'king': 'ملك',
        'queen': 'ملكة',
        'president': 'رئيس',
        'country': 'بلد',
        'city': 'مدينة',
        'capital': 'عاصمة',
        'language': 'لغة',
        'currency': 'عملة',
        'planet': 'كوكب',
        'animal': 'حيوان',
        'plant': 'نبات',
        'element': 'عنصر',
        'sport': 'رياضة',
        'team': 'فريق',
        'player': 'لاعب',
        'gold': 'ذهب',
        'silver': 'فضة',
        'bronze': 'برونز',
      },
    };

    // Vérification directe
    if (predefinedTranslations.containsKey(targetLanguage) &&
        predefinedTranslations[targetLanguage]!.containsKey(text)) {
      return predefinedTranslations[targetLanguage]![text]!;
    }

    // Traduction par mots-clés pour les phrases
    return _translateSentence(
      text,
      targetLanguage,
      predefinedTranslations[targetLanguage] ?? {},
    );
  }

  /// Traduction de phrases en remplaçant les mots connus
  String _translateSentence(
    String sentence,
    String targetLanguage,
    Map<String, String> dictionary,
  ) {
    String translatedSentence = sentence;

    // Remplacer les mots connus dans la phrase
    dictionary.forEach((english, translated) {
      // Remplacer les mots entiers (avec limites de mots)
      final regex = RegExp(
        r'\b' + RegExp.escape(english) + r'\b',
        caseSensitive: false,
      );
      translatedSentence = translatedSentence.replaceAll(regex, translated);
    });

    // Traductions spécifiques pour les structures de questions courantes
    if (targetLanguage == 'fr') {
      translatedSentence = _translateToFrench(translatedSentence);
    } else if (targetLanguage == 'ar') {
      translatedSentence = _translateToArabic(translatedSentence);
    }

    return translatedSentence;
  }

  /// Traductions spécifiques vers le français
  String _translateToFrench(String text) {
    final patterns = {
      // Structures de questions courantes
      r'In the film (.+), (.+)': 'Dans le film \$1, \$2',
      r'In the movie (.+), (.+)': 'Dans le film \$1, \$2',
      r'In the (.+), (.+)': 'Dans le \$1, \$2',
      r'What (.+) should (.+)': 'Quel \$1 devrait \$2',
      r'Which (.+) is (.+)': 'Quel \$1 est \$2',
      r'Who (.+) the (.+)': 'Qui \$1 le \$2',
      r'When (.+) was (.+)': 'Quand \$1 était \$2',
      r'Where (.+) is (.+)': 'Où \$1 est \$2',
      r'How (.+) does (.+)': 'Comment \$1 fait \$2',

      // Expressions spécifiques
      r'after what time of day': 'après quel moment de la journée',
      r'after what time': 'après quelle heure',
      r'should you not feed': 'ne devriez-vous pas nourrir',
      r'should you not': 'ne devriez-vous pas',
      r'time of day': 'moment de la journée',
      r'what time': 'quelle heure',

      // Mots simples
      r'\bfeed\b': 'nourrir',
      r'\bafter\b': 'après',
      r'\bwhat\b': 'quel',
      r'\btime\b': 'temps',
      r'\bday\b': 'jour',
      r'\bof\b': 'de',
      r'\bthe\b': 'le',
      r'\bin\b': 'dans',
      r'\byou\b': 'vous',
      r'\bnot\b': 'ne pas',
      r'\bshould\b': 'devriez',
    };

    String result = text;

    // Appliquer les patterns dans l'ordre (du plus spécifique au plus général)
    final orderedPatterns =
        patterns.entries.toList()
          ..sort((a, b) => b.key.length.compareTo(a.key.length));

    for (var entry in orderedPatterns) {
      result = result.replaceAll(
        RegExp(entry.key, caseSensitive: false),
        entry.value,
      );
    }

    return result;
  }

  /// Traductions spécifiques vers l'arabe
  String _translateToArabic(String text) {
    final patterns = {
      r'In the (.+), (.+)': 'في \$1، \$2',
      r'What (.+) should (.+)': 'ما \$1 يجب \$2',
      r'Which (.+) is (.+)': 'أي \$1 هو \$2',
      r'Who (.+) the (.+)': 'من \$1 ال\$2',
      r'When (.+) was (.+)': 'متى \$1 كان \$2',
      r'Where (.+) is (.+)': 'أين \$1 هو \$2',
      r'after what time': 'بعد أي وقت',
      r'should you not': 'يجب ألا',
      r'feed': 'تطعم',
      r'time of day': 'وقت من اليوم',
    };

    String result = text;
    patterns.forEach((pattern, replacement) {
      result = result.replaceAll(
        RegExp(pattern, caseSensitive: false),
        replacement,
      );
    });

    return result;
  }

  /// Traduit une liste de réponses
  Future<List<String>> translateAnswers(
    List<String> answers,
    String targetLanguage,
  ) async {
    if (targetLanguage == 'en') return answers;

    List<String> translatedAnswers = [];
    for (String answer in answers) {
      String translated = await translateText(answer, targetLanguage);
      translatedAnswers.add(translated);
    }
    return translatedAnswers;
  }

  /// Traduit une question spécifique avec des règles améliorées
  Future<String> translateQuestion(
    String question,
    String targetLanguage,
  ) async {
    if (targetLanguage == 'en') return question;

    // Traductions spécifiques pour les questions courantes
    final specificTranslations = _getSpecificQuestionTranslations(
      targetLanguage,
    );

    // Vérifier si on a une traduction directe
    if (specificTranslations.containsKey(question)) {
      return specificTranslations[question]!;
    }

    // Sinon, utiliser la traduction générale améliorée
    return await _translateQuestionWithContext(question, targetLanguage);
  }

  /// Traductions spécifiques pour les questions courantes
  Map<String, String> _getSpecificQuestionTranslations(String targetLanguage) {
    // Traductions directes pour les questions courantes
    if (targetLanguage == 'fr') {
      return {
        'How many dots are on a single die?':
            'Combien de points y a-t-il sur un seul dé ?',
        'What is the maximum level you can have in a single class in Dungeons and Dragons (5e)?':
            'Quel est le niveau maximum que vous pouvez avoir dans une seule classe dans Donjons et Dragons (5e) ?',
        'In the film "Gremlins", after what time of day should you not feed Mogwai?':
            'Dans le film "Gremlins", après quelle heure de la journée ne devriez-vous pas nourrir Mogwai ?',
        'In what year was Taylor Swift born?':
            'En quelle année Taylor Swift est-elle née ?',
        'What is the capital of Australia?':
            'Quelle est la capitale de l\'Australie ?',
        'What year was the first iPhone released?':
            'En quelle année le premier iPhone a-t-il été lancé ?',
        'Who painted the Mona Lisa?': 'Qui a peint la Joconde ?',
        'What is the largest planet in our solar system?':
            'Quelle est la plus grande planète de notre système solaire ?',
        'How many sides does a hexagon have?':
            'Combien de côtés a un hexagone ?',
        'What is the chemical symbol for gold?':
            'Quel est le symbole chimique de l\'or ?',
        'In which year did World War II end?':
            'En quelle année la Seconde Guerre mondiale s\'est-elle terminée ?',
      };
    } else if (targetLanguage == 'ar') {
      return {
        'How many dots are on a single die?':
            'كم عدد النقاط الموجودة على نرد واحد؟',
        'What is the maximum level you can have in a single class in Dungeons and Dragons (5e)?':
            'ما هو الحد الأقصى للمستوى الذي يمكن أن تحصل عليه في فئة واحدة في الأبراج المحصنة والتنانين (5e)؟',
        'In the film "Gremlins", after what time of day should you not feed Mogwai?':
            'في فيلم "Gremlins"، بعد أي وقت من اليوم يجب ألا تطعم Mogwai؟',
        'In what year was Taylor Swift born?': 'في أي سنة ولدت تايلور سويفت؟',
        'What is the capital of Australia?': 'ما هي عاصمة أستراليا؟',
        'What year was the first iPhone released?':
            'في أي عام تم إطلاق أول iPhone؟',
        'Who painted the Mona Lisa?': 'من رسم الموناليزا؟',
        'What is the largest planet in our solar system?':
            'ما هو أكبر كوكب في نظامنا الشمسي؟',
        'How many sides does a hexagon have?': 'كم عدد أضلاع المسدس؟',
        'What is the chemical symbol for gold?': 'ما هو الرمز الكيميائي للذهب؟',
        'In which year did World War II end?':
            'في أي عام انتهت الحرب العالمية الثانية؟',
      };
    }
    return {};
  }

  /// Traduction de question avec contexte amélioré
  Future<String> _translateQuestionWithContext(
    String question,
    String targetLanguage,
  ) async {
    String result = question;

    // Patterns de questions courantes
    final questionPatterns = _getQuestionPatterns(targetLanguage);

    // Appliquer les patterns
    for (var pattern in questionPatterns.entries) {
      final regex = RegExp(pattern.key, caseSensitive: false);
      if (regex.hasMatch(result)) {
        result = result.replaceAll(regex, pattern.value);
        break; // Utiliser le premier pattern qui correspond
      }
    }

    // Si aucun pattern ne correspond, utiliser la traduction mot par mot
    if (result == question) {
      result = await translateText(question, targetLanguage);
    }

    return result;
  }

  /// Patterns de questions pour traduction contextuelle
  Map<String, String> _getQuestionPatterns(String targetLanguage) {
    if (targetLanguage == 'fr') {
      return {
        r'How many (.+) are (.+)\?': 'Combien de \$1 sont \$2 ?',
        r'How many (.+) does (.+) have\?': 'Combien de \$1 a \$2 ?',
        r'What is the (.+) of (.+)\?': 'Quel est le \$1 de \$2 ?',
        r'What (.+) is (.+)\?': 'Quel \$1 est \$2 ?',
        r'Who (.+) the (.+)\?': 'Qui \$1 le \$2 ?',
        r'When (.+) was (.+)\?': 'Quand \$1 était \$2 ?',
        r'Where (.+) is (.+)\?': 'Où \$1 est \$2 ?',
        r'In which (.+) did (.+)\?': 'En quelle \$1 \$2 ?',
        r'In the (.+) "(.+)", (.+)\?': 'Dans le \$1 "\$2", \$3 ?',
      };
    } else if (targetLanguage == 'ar') {
      return {
        r'How many (.+) are (.+)\?': 'كم عدد \$1 \$2؟',
        r'How many (.+) does (.+) have\?': 'كم عدد \$1 لدى \$2؟',
        r'What is the (.+) of (.+)\?': 'ما هو \$1 \$2؟',
        r'What (.+) is (.+)\?': 'ما \$1 \$2؟',
        r'Who (.+) the (.+)\?': 'من \$1 \$2؟',
        r'When (.+) was (.+)\?': 'متى كان \$1 \$2؟',
        r'Where (.+) is (.+)\?': 'أين \$1 \$2؟',
        r'In which (.+) did (.+)\?': 'في أي \$1 \$2؟',
        r'In the (.+) "(.+)", (.+)\?': 'في \$1 "\$2"، \$3؟',
      };
    }
    return {};
  }

  /// Nettoie le cache de traduction
  void clearCache() {
    _translationCache.clear();
  }
}
