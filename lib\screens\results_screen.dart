import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import 'welcome_screen.dart';

class ResultsScreen extends StatelessWidget {
  const ResultsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final quizProvider = Provider.of<QuizProvider>(context);
    final l10n = AppLocalizations.of(context);
    final score = quizProvider.score;
    final totalQuestions = quizProvider.questions.length;
    final percentage = (score / totalQuestions) * 100;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.yourScore),
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            // Score summary
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    Text(
                      l10n.yourScore,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      '$score / $totalQuestions',
                      style: const TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      '${percentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: _getColorForPercentage(percentage),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      _getFeedbackMessage(percentage, l10n),
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 18),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Question review
            Text(
              l10n.questionReview,
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),

            Expanded(
              child: ListView.builder(
                itemCount: quizProvider.questions.length,
                itemBuilder: (context, index) {
                  final question = quizProvider.questions[index];
                  final userAnswer = quizProvider.selectedAnswers[index];
                  final isCorrect = quizProvider.userAnswers[index] ?? false;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 10),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                isCorrect ? Icons.check_circle : Icons.cancel,
                                color: isCorrect ? Colors.green : Colors.red,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  question.displayQuestion,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${l10n.yourAnswer}: $userAnswer',
                            style: TextStyle(
                              color: isCorrect ? Colors.green : Colors.red,
                            ),
                          ),
                          if (!isCorrect) ...[
                            const SizedBox(height: 4),
                            Text(
                              '${l10n.correctAnswer}: ${question.displayCorrectAnswer}',
                              style: const TextStyle(color: Colors.green),
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WelcomeScreen(),
                      ),
                      (route) => false,
                    );
                  },
                  icon: const Icon(Icons.home),
                  label: Text(l10n.home),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    quizProvider.resetQuiz();
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.refresh),
                  label: Text(l10n.newQuiz),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getColorForPercentage(double percentage) {
    if (percentage >= 80) {
      return Colors.green;
    } else if (percentage >= 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  String _getFeedbackMessage(double percentage, AppLocalizations l10n) {
    if (percentage >= 80) {
      return 'Excellent ! / Excellent! / ممتاز!';
    } else if (percentage >= 60) {
      return 'Bon travail ! / Good job! / عمل جيد!';
    } else if (percentage >= 40) {
      return 'Pas mal / Not bad / ليس سيئا';
    } else {
      return 'Continuez à pratiquer / Keep practicing / استمر في الممارسة';
    }
  }
}
