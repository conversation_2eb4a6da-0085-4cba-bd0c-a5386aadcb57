import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class AuthService extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  User? get currentUser => _auth.currentUser;
  bool get isAuthenticated => _auth.currentUser != null;
  
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Inscription avec email et mot de passe
  Future<UserCredential?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String displayName,
  }) async {
    try {
      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Mettre à jour le profil utilisateur
      await result.user?.updateDisplayName(displayName);

      // Créer le document utilisateur dans Firestore
      if (result.user != null) {
        await _createUserDocument(result.user!, displayName);
      }

      notifyListeners();
      return result;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'Une erreur inattendue s\'est produite';
    }
  }

  // Connexion avec email et mot de passe
  Future<UserCredential?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      notifyListeners();
      return result;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'Une erreur inattendue s\'est produite';
    }
  }

  // Déconnexion
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      notifyListeners();
    } catch (e) {
      throw 'Erreur lors de la déconnexion';
    }
  }

  // Réinitialisation du mot de passe
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'Une erreur inattendue s\'est produite';
    }
  }

  // Créer le document utilisateur dans Firestore
  Future<void> _createUserDocument(User user, String displayName) async {
    try {
      await _firestore.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'email': user.email,
        'displayName': displayName,
        'createdAt': FieldValue.serverTimestamp(),
        'totalQuizzes': 0,
        'bestScore': 0,
        'averageScore': 0.0,
      });
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la création du document utilisateur: $e');
      }
    }
  }

  // Obtenir les données utilisateur depuis Firestore
  Future<Map<String, dynamic>?> getUserData() async {
    if (currentUser == null) return null;
    
    try {
      final doc = await _firestore.collection('users').doc(currentUser!.uid).get();
      return doc.data();
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la récupération des données utilisateur: $e');
      }
      return null;
    }
  }

  // Mettre à jour les statistiques utilisateur
  Future<void> updateUserStats({
    required int score,
    required int totalQuestions,
    required String category,
    required String difficulty,
  }) async {
    if (currentUser == null) return;

    try {
      final userRef = _firestore.collection('users').doc(currentUser!.uid);
      final userData = await getUserData();
      
      if (userData != null) {
        final int currentTotalQuizzes = userData['totalQuizzes'] ?? 0;
        final int currentBestScore = userData['bestScore'] ?? 0;
        final double currentAverageScore = userData['averageScore']?.toDouble() ?? 0.0;
        
        final double percentage = (score / totalQuestions) * 100;
        final int newTotalQuizzes = currentTotalQuizzes + 1;
        final int newBestScore = percentage > currentBestScore ? percentage.round() : currentBestScore;
        final double newAverageScore = ((currentAverageScore * currentTotalQuizzes) + percentage) / newTotalQuizzes;

        await userRef.update({
          'totalQuizzes': newTotalQuizzes,
          'bestScore': newBestScore,
          'averageScore': newAverageScore,
          'lastQuizDate': FieldValue.serverTimestamp(),
        });

        // Ajouter le score individuel
        await _firestore.collection('users').doc(currentUser!.uid).collection('scores').add({
          'score': score,
          'totalQuestions': totalQuestions,
          'percentage': percentage,
          'category': category,
          'difficulty': difficulty,
          'date': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la mise à jour des statistiques: $e');
      }
    }
  }

  // Gestion des erreurs d'authentification
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return 'Le mot de passe est trop faible';
      case 'email-already-in-use':
        return 'Un compte existe déjà avec cette adresse email';
      case 'user-not-found':
        return 'Aucun utilisateur trouvé avec cette adresse email';
      case 'wrong-password':
        return 'Mot de passe incorrect';
      case 'invalid-email':
        return 'Adresse email invalide';
      case 'user-disabled':
        return 'Ce compte a été désactivé';
      case 'too-many-requests':
        return 'Trop de tentatives. Veuillez réessayer plus tard';
      case 'operation-not-allowed':
        return 'Cette opération n\'est pas autorisée';
      default:
        return 'Erreur d\'authentification: ${e.message}';
    }
  }
}
