import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

import 'widgets/auth_wrapper.dart';
import 'services/quiz_provider.dart';
import 'services/theme_provider.dart';
import 'services/simple_auth_service.dart';
// import 'services/notification_service.dart';
// import 'services/audio_service.dart';
// import 'services/vibration_service.dart';
import 'services/localization_service.dart';
import 'services/score_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize services (temporarily simplified)
  // final notificationService = NotificationService();
  // await notificationService.initialize();

  // final audioService = AudioService();
  // await audioService.initialize();

  // final vibrationService = VibrationService();
  // await vibrationService.initialize();

  final localizationService = LocalizationService();
  await localizationService.initialize();

  final scoreService = ScoreService();
  await scoreService.initialize();

  runApp(
    MyApp(localizationService: localizationService, scoreService: scoreService),
  );
}

class MyApp extends StatelessWidget {
  final LocalizationService localizationService;
  final ScoreService scoreService;

  const MyApp({
    super.key,
    required this.localizationService,
    required this.scoreService,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) {
            final quizProvider = QuizProvider();
            quizProvider.setLocalizationService(localizationService);
            return quizProvider;
          },
        ),
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => AuthService()),
        ChangeNotifierProvider.value(value: localizationService),
        ChangeNotifierProvider.value(value: scoreService),
      ],
      child: Consumer3<ThemeProvider, LocalizationService, AuthService>(
        builder: (
          context,
          themeProvider,
          localizationService,
          authService,
          child,
        ) {
          // Connecter AuthService et ScoreService
          scoreService.setAuthService(authService);

          return MaterialApp(
            key: ValueKey(
              localizationService.locale.languageCode,
            ), // Forcer la reconstruction
            title: 'Quiz App',
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.themeMode,
            locale: localizationService.locale,
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: localizationService.supportedLocales,
            home: const AuthWrapper(),
          );
        },
      ),
    );
  }
}
