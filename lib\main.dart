import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'l10n/app_localizations.dart';
import 'package:provider/provider.dart';

import 'screens/welcome_screen.dart';
import 'services/quiz_provider.dart';
import 'services/theme_provider.dart';
// import 'services/notification_service.dart';
// import 'services/audio_service.dart';
// import 'services/vibration_service.dart';
import 'services/localization_service.dart';
import 'services/score_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services (temporarily simplified)
  // final notificationService = NotificationService();
  // await notificationService.initialize();

  // final audioService = AudioService();
  // await audioService.initialize();

  // final vibrationService = VibrationService();
  // await vibrationService.initialize();

  final localizationService = LocalizationService();
  await localizationService.initialize();

  final scoreService = ScoreService();
  await scoreService.initialize();

  runApp(MyApp(localizationService: localizationService));
}

class MyApp extends StatelessWidget {
  final LocalizationService localizationService;

  const MyApp({super.key, required this.localizationService});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => QuizProvider()),
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider.value(value: localizationService),
      ],
      child: Consumer2<ThemeProvider, LocalizationService>(
        builder: (context, themeProvider, localizationService, child) {
          return MaterialApp(
            title: 'Quiz App',
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.themeMode,
            locale: localizationService.locale,
            localizationsDelegates: [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: localizationService.supportedLocales,
            home: const WelcomeScreen(),
          );
        },
      ),
    );
  }
}
