import 'package:vibration/vibration.dart';
import 'package:shared_preferences/shared_preferences.dart';

class VibrationService {
  static final VibrationService _instance = VibrationService._internal();
  factory VibrationService() => _instance;
  VibrationService._internal();

  bool _isVibrationEnabled = true;
  bool _hasVibrator = false;

  Future<void> initialize() async {
    // Check if device has vibrator
    _hasVibrator = await Vibration.hasVibrator() ?? false;
    
    // Load user preference
    final prefs = await SharedPreferences.getInstance();
    _isVibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
  }

  Future<void> vibrateOnAnswer(bool isCorrect) async {
    if (!_isVibrationEnabled || !_hasVibrator) return;
    
    if (isCorrect) {
      // Short vibration for correct answer
      Vibration.vibrate(duration: 100);
    } else {
      // Pattern vibration for incorrect answer
      Vibration.vibrate(pattern: [0, 100, 100, 100]);
    }
  }

  Future<void> vibrateOnButtonPress() async {
    if (!_isVibrationEnabled || !_hasVibrator) return;
    Vibration.vibrate(duration: 50);
  }

  Future<void> setVibrationEnabled(bool enabled) async {
    _isVibrationEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('vibration_enabled', enabled);
  }

  bool get isVibrationEnabled => _isVibrationEnabled;
  bool get hasVibrator => _hasVibrator;
}
