import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/localization_service.dart';

/// Widget qui s'adapte automatiquement à la direction RTL/LTR
class RTLAwareWidget extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final bool reverseForRTL;

  const RTLAwareWidget({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.reverseForRTL = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return Directionality(
          textDirection: localizationService.textDirection,
          child: Container(
            padding: padding,
            margin: margin,
            child: reverseForRTL && localizationService.isRTL
                ? _reverseChild(child)
                : child,
          ),
        );
      },
    );
  }

  Widget _reverseChild(Widget child) {
    if (child is Row) {
      return Row(
        mainAxisAlignment: child.mainAxisAlignment,
        mainAxisSize: child.mainAxisSize,
        crossAxisAlignment: child.crossAxisAlignment,
        textDirection: TextDirection.rtl,
        verticalDirection: child.verticalDirection,
        textBaseline: child.textBaseline,
        children: child.children.reversed.toList(),
      );
    }
    return child;
  }
}

/// Widget de texte adaptatif pour RTL
class RTLAwareText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const RTLAwareText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return Text(
          text,
          style: style,
          textAlign: textAlign ?? localizationService.textAlign,
          maxLines: maxLines,
          overflow: overflow,
          textDirection: localizationService.textDirection,
        );
      },
    );
  }
}

/// Card avec animation et support RTL
class AnimatedQuizCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? elevation;

  const AnimatedQuizCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
  });

  @override
  State<AnimatedQuizCard> createState() => _AnimatedQuizCardState();
}

class _AnimatedQuizCardState extends State<AnimatedQuizCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _elevationAnimation = Tween<double>(
      begin: widget.elevation ?? 4.0,
      end: (widget.elevation ?? 4.0) + 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                margin: widget.margin,
                child: Material(
                  elevation: _elevationAnimation.value,
                  borderRadius: BorderRadius.circular(12),
                  color: widget.backgroundColor ?? Theme.of(context).cardColor,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: widget.onTap,
                    onTapDown: (_) => _controller.forward(),
                    onTapUp: (_) => _controller.reverse(),
                    onTapCancel: () => _controller.reverse(),
                    child: Directionality(
                      textDirection: localizationService.textDirection,
                      child: Container(
                        padding: widget.padding ?? const EdgeInsets.all(16),
                        child: widget.child,
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

/// Bouton adaptatif avec animations
class AnimatedQuizButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final EdgeInsetsGeometry? padding;
  final bool isLoading;

  const AnimatedQuizButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.padding,
    this.isLoading = false,
  });

  @override
  State<AnimatedQuizButton> createState() => _AnimatedQuizButtonState();
}

class _AnimatedQuizButtonState extends State<AnimatedQuizButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: ElevatedButton(
                onPressed: widget.isLoading ? null : widget.onPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.backgroundColor,
                  foregroundColor: widget.textColor,
                  padding: widget.padding ??
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onLongPress: () {
                  _controller.forward().then((_) => _controller.reverse());
                },
                child: Directionality(
                  textDirection: localizationService.textDirection,
                  child: widget.isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (widget.icon != null && !localizationService.isRTL) ...[
                              Icon(widget.icon),
                              const SizedBox(width: 8),
                            ],
                            Text(widget.text),
                            if (widget.icon != null && localizationService.isRTL) ...[
                              const SizedBox(width: 8),
                              Icon(widget.icon),
                            ],
                          ],
                        ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
