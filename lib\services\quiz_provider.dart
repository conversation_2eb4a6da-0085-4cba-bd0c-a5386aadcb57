import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/question_model.dart';
import 'translation_service.dart';
import 'localization_service.dart';

class QuizProvider extends ChangeNotifier {
  List<Question> _questions = [];
  List<Question> get questions => _questions;

  int _currentQuestionIndex = 0;
  int get currentQuestionIndex => _currentQuestionIndex;

  int _score = 0;
  int get score => _score;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> get categories => _categories;

  // Méthode pour définir le service de localisation
  void setLocalizationService(LocalizationService localizationService) {
    _localizationService?.removeListener(_onLanguageChanged);
    _localizationService = localizationService;
    _localizationService?.addListener(_onLanguageChanged);
    notifyListeners();
  }

  // Callback quand la langue change
  void _onLanguageChanged() {
    notifyListeners(); // Notifier les widgets qui utilisent translatedCategories et translatedDifficulties
  }

  @override
  void dispose() {
    _localizationService?.removeListener(_onLanguageChanged);
    super.dispose();
  }

  // Obtenir les catégories traduites
  List<Map<String, dynamic>> get translatedCategories {
    final currentLanguage = _localizationService?.locale.languageCode ?? 'fr';

    return _categories.map((category) {
      final originalName = category['name'] as String;
      final translatedName = _translationService.translateCategory(
        originalName,
        currentLanguage,
      );

      return {
        ...category,
        'name': translatedName,
        'originalName': originalName,
      };
    }).toList();
  }

  // Services de traduction
  final TranslationService _translationService = TranslationService();
  LocalizationService? _localizationService;

  final List<String> _difficulties = ['easy', 'medium', 'hard'];
  List<String> get difficulties => _difficulties;

  // Obtenir les difficultés traduites
  List<Map<String, String>> get translatedDifficulties {
    final currentLanguage = _localizationService?.locale.languageCode ?? 'fr';

    return _difficulties.map((difficulty) {
      final translatedName = _translationService.translateDifficulty(
        difficulty,
        currentLanguage,
      );

      return {'value': difficulty, 'name': translatedName};
    }).toList();
  }

  final List<int> _questionCounts = [5, 10, 15, 20];
  List<int> get questionCounts => _questionCounts;

  List<bool?> _userAnswers = [];
  List<bool?> get userAnswers => _userAnswers;

  List<String> _selectedAnswers = [];
  List<String> get selectedAnswers => _selectedAnswers;

  // Initialize the provider
  Future<void> initialize() async {
    await fetchCategories();
  }

  // Fetch categories from the API
  Future<void> fetchCategories() async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse('https://opentdb.com/api_category.php'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _categories = List<Map<String, dynamic>>.from(
          data['trivia_categories'],
        );
      }
    } catch (e) {
      debugPrint('Error fetching categories: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch questions from the API
  Future<void> fetchQuestions({
    required int categoryId,
    required String difficulty,
    required int amount,
  }) async {
    _isLoading = true;
    _questions = [];
    _currentQuestionIndex = 0;
    _score = 0;
    _userAnswers = List.filled(amount, null);
    _selectedAnswers = List.filled(amount, '');
    notifyListeners();

    try {
      final url =
          'https://opentdb.com/api.php?amount=$amount&category=$categoryId&difficulty=$difficulty&type=multiple&encode=base64';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['response_code'] == 0) {
          final results = data['results'] as List;
          _questions =
              results.map((questionData) {
                // Decode base64 encoded strings
                Map<String, dynamic> decodedData = {};
                questionData.forEach((key, value) {
                  if (value is String) {
                    decodedData[key] = utf8.decode(base64.decode(value));
                  } else if (value is List) {
                    decodedData[key] =
                        value
                            .map((item) => utf8.decode(base64.decode(item)))
                            .toList();
                  } else {
                    decodedData[key] = value;
                  }
                });
                return Question.fromJson(decodedData);
              }).toList();

          // Traduire les questions si nécessaire
          await _translateQuestions();
        }
      }
    } catch (e) {
      debugPrint('Error fetching questions: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Traduire toutes les questions selon la langue actuelle
  Future<void> _translateQuestions() async {
    final currentLanguage = _localizationService?.locale.languageCode ?? 'fr';

    if (currentLanguage == 'en') {
      // Pas besoin de traduction pour l'anglais
      return;
    }

    for (Question question in _questions) {
      try {
        // Traduire la question
        final translatedQuestion = await _translationService.translateQuestion(
          question.question,
          currentLanguage,
        );

        // Traduire la bonne réponse
        final translatedCorrectAnswer = await _translationService.translateText(
          question.correctAnswer,
          currentLanguage,
        );

        // Traduire les mauvaises réponses
        final translatedIncorrectAnswers = await _translationService
            .translateAnswers(question.incorrectAnswers, currentLanguage);

        // Traduire la catégorie
        final translatedCategory = _translationService.translateCategory(
          question.category,
          currentLanguage,
        );

        // Appliquer les traductions
        question.setTranslations(
          question: translatedQuestion,
          correctAnswer: translatedCorrectAnswer,
          incorrectAnswers: translatedIncorrectAnswers,
          category: translatedCategory,
        );
      } catch (e) {
        debugPrint('Error translating question: $e');
      }
    }
  }

  // Answer the current question
  void answerQuestion(String selectedAnswer) {
    final currentQuestion = _questions[_currentQuestionIndex];

    // Vérifier si la réponse est correcte en comparant avec la version traduite ou originale
    final correctAnswer = currentQuestion.displayCorrectAnswer;
    final isCorrect = selectedAnswer == correctAnswer;

    if (isCorrect) {
      _score++;
    }

    _userAnswers[_currentQuestionIndex] = isCorrect;
    _selectedAnswers[_currentQuestionIndex] = selectedAnswer;
    notifyListeners();
  }

  // Move to the next question
  void nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      _currentQuestionIndex++;
      notifyListeners();
    }
  }

  // Reset the quiz
  void resetQuiz() {
    _questions = [];
    _currentQuestionIndex = 0;
    _score = 0;
    _userAnswers = [];
    _selectedAnswers = [];
    notifyListeners();
  }

  // Save high score
  Future<void> saveHighScore(int score) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final highScore = prefs.getInt('highScore') ?? 0;

      if (score > highScore) {
        await prefs.setInt('highScore', score);
      }
    } catch (e) {
      debugPrint('Error saving high score: $e');
    }
  }

  // Get high score
  Future<int> getHighScore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt('highScore') ?? 0;
    } catch (e) {
      debugPrint('Error getting high score: $e');
      return 0;
    }
  }
}
