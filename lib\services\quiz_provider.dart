import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/question_model.dart';

class QuizProvider extends ChangeNotifier {
  List<Question> _questions = [];
  List<Question> get questions => _questions;

  int _currentQuestionIndex = 0;
  int get currentQuestionIndex => _currentQuestionIndex;

  int _score = 0;
  int get score => _score;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  List<Map<String, dynamic>> _categories = [];
  List<Map<String, dynamic>> get categories => _categories;

  final List<String> _difficulties = ['easy', 'medium', 'hard'];
  List<String> get difficulties => _difficulties;

  final List<int> _questionCounts = [5, 10, 15, 20];
  List<int> get questionCounts => _questionCounts;

  List<bool?> _userAnswers = [];
  List<bool?> get userAnswers => _userAnswers;

  List<String> _selectedAnswers = [];
  List<String> get selectedAnswers => _selectedAnswers;

  // Initialize the provider
  Future<void> initialize() async {
    await fetchCategories();
  }

  // Fetch categories from the API
  Future<void> fetchCategories() async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse('https://opentdb.com/api_category.php'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _categories = List<Map<String, dynamic>>.from(
          data['trivia_categories'],
        );
      }
    } catch (e) {
      debugPrint('Error fetching categories: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch questions from the API
  Future<void> fetchQuestions({
    required int categoryId,
    required String difficulty,
    required int amount,
  }) async {
    _isLoading = true;
    _questions = [];
    _currentQuestionIndex = 0;
    _score = 0;
    _userAnswers = List.filled(amount, null);
    _selectedAnswers = List.filled(amount, '');
    notifyListeners();

    try {
      final url =
          'https://opentdb.com/api.php?amount=$amount&category=$categoryId&difficulty=$difficulty&type=multiple&encode=base64';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['response_code'] == 0) {
          final results = data['results'] as List;
          _questions =
              results.map((questionData) {
                // Decode base64 encoded strings
                Map<String, dynamic> decodedData = {};
                questionData.forEach((key, value) {
                  if (value is String) {
                    decodedData[key] = utf8.decode(base64.decode(value));
                  } else if (value is List) {
                    decodedData[key] =
                        value
                            .map((item) => utf8.decode(base64.decode(item)))
                            .toList();
                  } else {
                    decodedData[key] = value;
                  }
                });
                return Question.fromJson(decodedData);
              }).toList();
        }
      }
    } catch (e) {
      debugPrint('Error fetching questions: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Answer the current question
  void answerQuestion(String selectedAnswer) {
    final currentQuestion = _questions[_currentQuestionIndex];
    final isCorrect = selectedAnswer == currentQuestion.correctAnswer;

    if (isCorrect) {
      _score++;
    }

    _userAnswers[_currentQuestionIndex] = isCorrect;
    _selectedAnswers[_currentQuestionIndex] = selectedAnswer;
    notifyListeners();
  }

  // Move to the next question
  void nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      _currentQuestionIndex++;
      notifyListeners();
    }
  }

  // Reset the quiz
  void resetQuiz() {
    _questions = [];
    _currentQuestionIndex = 0;
    _score = 0;
    _userAnswers = [];
    _selectedAnswers = [];
    notifyListeners();
  }

  // Save high score
  Future<void> saveHighScore(int score) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final highScore = prefs.getInt('highScore') ?? 0;

      if (score > highScore) {
        await prefs.setInt('highScore', score);
      }
    } catch (e) {
      debugPrint('Error saving high score: $e');
    }
  }

  // Get high score
  Future<int> getHighScore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt('highScore') ?? 0;
    } catch (e) {
      debugPrint('Error getting high score: $e');
      return 0;
    }
  }
}
