import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/localization_service.dart';

class LanguageSwitcher extends StatelessWidget {
  const LanguageSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, child) {
        final currentLanguage = localizationService.locale.languageCode;
        
        return PopupMenuButton<String>(
          icon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.language, color: Theme.of(context).colorScheme.onPrimary),
              const SizedBox(width: 4),
              Text(
                _getLanguageFlag(currentLanguage),
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          onSelected: (String languageCode) async {
            final locale = Locale(languageCode);
            await localizationService.setLocale(locale);
          },
          itemBuilder: (BuildContext context) => [
            PopupMenuItem<String>(
              value: 'en',
              child: Row(
                children: [
                  const Text('🇺🇸', style: TextStyle(fontSize: 20)),
                  const SizedBox(width: 8),
                  Text(localizationService.getLanguageName('en')),
                  if (currentLanguage == 'en') ...[
                    const Spacer(),
                    const Icon(Icons.check, color: Colors.green),
                  ],
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'fr',
              child: Row(
                children: [
                  const Text('🇫🇷', style: TextStyle(fontSize: 20)),
                  const SizedBox(width: 8),
                  Text(localizationService.getLanguageName('fr')),
                  if (currentLanguage == 'fr') ...[
                    const Spacer(),
                    const Icon(Icons.check, color: Colors.green),
                  ],
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'ar',
              child: Row(
                children: [
                  const Text('🇸🇦', style: TextStyle(fontSize: 20)),
                  const SizedBox(width: 8),
                  Text(localizationService.getLanguageName('ar')),
                  if (currentLanguage == 'ar') ...[
                    const Spacer(),
                    const Icon(Icons.check, color: Colors.green),
                  ],
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  String _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'ar':
        return '🇸🇦';
      default:
        return '🌐';
    }
  }
}
