import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import '../services/localization_service.dart';
import '../widgets/rtl_aware_widget.dart';
import '../widgets/language_switcher.dart';
import 'quiz_screen.dart';

class QuizSettingsScreen extends StatefulWidget {
  const QuizSettingsScreen({super.key});

  @override
  State<QuizSettingsScreen> createState() => _QuizSettingsScreenState();
}

class _QuizSettingsScreenState extends State<QuizSettingsScreen> {
  int? _selectedCategoryId;
  String _selectedDifficulty = 'medium';
  int _selectedQuestionCount = 10;

  @override
  Widget build(BuildContext context) {
    final quizProvider = Provider.of<QuizProvider>(context);
    final l10n = AppLocalizations.of(context);

    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return Directionality(
          textDirection: localizationService.textDirection,
          child: Scaffold(
            appBar: AppBar(
              title: RTLAwareText(
                l10n.quizSettings,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              actions: const [LanguageSwitcher(), SizedBox(width: 8)],
            ),
            body:
                quizProvider.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : SingleChildScrollView(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Section Catégorie
                          AnimatedQuizCard(
                            margin: const EdgeInsets.only(bottom: 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                RTLAwareText(
                                  l10n.selectCategory,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                _buildCategoryDropdown(
                                  quizProvider,
                                  l10n,
                                  localizationService,
                                ),
                              ],
                            ),
                          ),

                          // Section Difficulté
                          AnimatedQuizCard(
                            margin: const EdgeInsets.only(bottom: 20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                RTLAwareText(
                                  l10n.selectDifficulty,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                _buildDifficultySelector(
                                  l10n,
                                  localizationService,
                                ),
                              ],
                            ),
                          ),

                          // Section Nombre de questions
                          AnimatedQuizCard(
                            margin: const EdgeInsets.only(bottom: 30),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                RTLAwareText(
                                  l10n.numberOfQuestions,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                _buildQuestionCountSelector(
                                  quizProvider,
                                  localizationService,
                                ),
                              ],
                            ),
                          ),

                          // Bouton de démarrage
                          Center(
                            child: AnimatedQuizButton(
                              text: l10n.startQuiz,
                              icon: Icons.play_arrow,
                              onPressed:
                                  _selectedCategoryId == null
                                      ? null
                                      : () => _startQuiz(context),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 48,
                                vertical: 16,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryDropdown(
    QuizProvider quizProvider,
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        borderRadius: BorderRadius.circular(12),
      ),
      child: DropdownButton<int>(
        value: _selectedCategoryId,
        isExpanded: true,
        hint: RTLAwareText(l10n.selectCategory),
        underline: const SizedBox(),
        items:
            quizProvider.translatedCategories.map((category) {
              return DropdownMenuItem<int>(
                value: category['id'],
                child: RTLAwareText(category['name']),
              );
            }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedCategoryId = value;
          });
        },
      ),
    );
  }

  Widget _buildDifficultySelector(
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    return Consumer<QuizProvider>(
      builder: (context, quizProvider, child) {
        return Row(
          children:
              quizProvider.translatedDifficulties.map((difficulty) {
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: _buildDifficultyOption(
                      difficulty['name']!,
                      difficulty['value']!,
                      localizationService,
                    ),
                  ),
                );
              }).toList(),
        );
      },
    );
  }

  Widget _buildDifficultyOption(
    String label,
    String value,
    LocalizationService localizationService,
  ) {
    final isSelected = _selectedDifficulty == value;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedDifficulty = value;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline,
            ),
          ),
          child: RTLAwareText(
            label,
            style: TextStyle(
              color:
                  isSelected
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionCountSelector(
    QuizProvider quizProvider,
    LocalizationService localizationService,
  ) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children:
          quizProvider.questionCounts.map((count) {
            final isSelected = _selectedQuestionCount == count;
            return AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedQuestionCount = count;
                  });
                },
                borderRadius: BorderRadius.circular(50),
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.surface,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.outline,
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: RTLAwareText(
                      count.toString(),
                      style: TextStyle(
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }

  void _startQuiz(BuildContext context) async {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    final l10n = AppLocalizations.of(context);

    // Show loading dialog with RTL support
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Consumer<LocalizationService>(
            builder: (context, localizationService, _) {
              return Directionality(
                textDirection: localizationService.textDirection,
                child: AlertDialog(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 20),
                      RTLAwareText(
                        l10n.loading,
                        style: const TextStyle(fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
    );

    try {
      // Fetch questions
      await quizProvider.fetchQuestions(
        categoryId: _selectedCategoryId!,
        difficulty: _selectedDifficulty,
        amount: _selectedQuestionCount,
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);
      }

      // Navigate to quiz screen if questions were loaded successfully
      if (context.mounted && quizProvider.questions.isNotEmpty) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const QuizScreen()),
        );
      } else if (context.mounted) {
        // Show error message if no questions were loaded
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: RTLAwareText(l10n.loadingError),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog on error
      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: RTLAwareText(l10n.loadingError),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }
}
