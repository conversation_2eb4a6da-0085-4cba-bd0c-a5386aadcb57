import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import 'quiz_screen.dart';

class QuizSettingsScreen extends StatefulWidget {
  const QuizSettingsScreen({super.key});

  @override
  State<QuizSettingsScreen> createState() => _QuizSettingsScreenState();
}

class _QuizSettingsScreenState extends State<QuizSettingsScreen> {
  int? _selectedCategoryId;
  String _selectedDifficulty = 'medium';
  int _selectedQuestionCount = 10;

  @override
  Widget build(BuildContext context) {
    final quizProvider = Provider.of<QuizProvider>(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(l10n.quizSettings)),
      body:
          quizProvider.isLoading
              ? const Center(child: CircularProgressIndicator())
              : Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.selectCategory,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    _buildCategoryDropdown(quizProvider, l10n),
                    const SizedBox(height: 20),
                    Text(
                      l10n.selectDifficulty,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    _buildDifficultySelector(),
                    const SizedBox(height: 20),
                    Text(
                      l10n.numberOfQuestions,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    _buildQuestionCountSelector(quizProvider),
                    const SizedBox(height: 40),
                    Center(
                      child: ElevatedButton(
                        onPressed:
                            _selectedCategoryId == null
                                ? null
                                : () => _startQuiz(context),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 40,
                            vertical: 15,
                          ),
                        ),
                        child: Text(
                          l10n.startQuiz,
                          style: const TextStyle(fontSize: 18),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildCategoryDropdown(
    QuizProvider quizProvider,
    AppLocalizations l10n,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<int>(
        value: _selectedCategoryId,
        isExpanded: true,
        hint: Text(l10n.selectCategory),
        underline: const SizedBox(),
        items:
            quizProvider.translatedCategories.map((category) {
              return DropdownMenuItem<int>(
                value: category['id'],
                child: Text(category['name']),
              );
            }).toList(),
        onChanged: (value) {
          setState(() {
            _selectedCategoryId = value;
          });
        },
      ),
    );
  }

  Widget _buildDifficultySelector() {
    return Consumer<QuizProvider>(
      builder: (context, quizProvider, child) {
        return Row(
          children:
              quizProvider.translatedDifficulties.map((difficulty) {
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: _buildDifficultyOption(
                      difficulty['name']!,
                      difficulty['value']!,
                    ),
                  ),
                );
              }).toList(),
        );
      },
    );
  }

  Widget _buildDifficultyOption(String label, String value) {
    return InkWell(
      onTap: () {
        setState(() {
          _selectedDifficulty = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color:
              _selectedDifficulty == value ? Colors.blue : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: _selectedDifficulty == value ? Colors.white : Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildQuestionCountSelector(QuizProvider quizProvider) {
    return Wrap(
      spacing: 8,
      children:
          quizProvider.questionCounts.map((count) {
            return InkWell(
              onTap: () {
                setState(() {
                  _selectedQuestionCount = count;
                });
              },
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color:
                      _selectedQuestionCount == count
                          ? Colors.blue
                          : Colors.grey.shade200,
                  shape: BoxShape.circle,
                ),
                child: Text(
                  count.toString(),
                  style: TextStyle(
                    color:
                        _selectedQuestionCount == count
                            ? Colors.white
                            : Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }

  void _startQuiz(BuildContext context) async {
    final quizProvider = Provider.of<QuizProvider>(context, listen: false);
    final l10n = AppLocalizations.of(context);

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(l10n.loading),
              ],
            ),
          ),
    );

    // Fetch questions
    await quizProvider.fetchQuestions(
      categoryId: _selectedCategoryId!,
      difficulty: _selectedDifficulty,
      amount: _selectedQuestionCount,
    );

    // Close loading dialog
    if (context.mounted) {
      Navigator.pop(context);
    }

    // Navigate to quiz screen if questions were loaded successfully
    if (context.mounted && quizProvider.questions.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const QuizScreen()),
      );
    } else if (context.mounted) {
      // Show error message if no questions were loaded
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.loadingError), backgroundColor: Colors.red),
      );
    }
  }
}
