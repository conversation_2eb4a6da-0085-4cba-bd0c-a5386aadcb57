import 'package:flutter/material.dart';

// Classe temporaire pour résoudre les erreurs d'importation
// Cette classe sera remplacée par la classe générée par Flutter
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations) ??
        AppLocalizations(const Locale('fr'));
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // Propriétés pour les traductions
  String get appTitle => 'Quiz App';
  String get welcomeMessage => 'Bienvenue sur Quiz App !';
  String get welcomeDescription =>
      'Testez vos connaissances avec des quiz de différentes catégories';
  String get startQuiz => 'Commencer un quiz';
  String get about => 'À propos';
  String get aboutTitle => 'À propos de Quiz App';
  String get aboutDescription =>
      'Quiz App est une application de quiz interactive qui vous permet de tester vos connaissances dans diverses catégories.';
  String get aboutApi =>
      'Les questions sont récupérées depuis l\'API Open Trivia Database (OpenTDB).';
  String get aboutFeatures => 'Fonctionnalités :';
  String get aboutFeature1 => '• Plusieurs catégories';
  String get aboutFeature2 => '• Différents niveaux de difficulté';
  String get aboutFeature3 => '• Suivi des scores';
  String get aboutFeature4 => '• Retour immédiat';
  String get close => 'Fermer';
  String get quizSettings => 'Paramètres du quiz';
  String get selectCategory => 'Sélectionner une catégorie :';
  String get selectDifficulty => 'Sélectionner la difficulté :';
  String get numberOfQuestions => 'Nombre de questions :';
  String get easy => 'Facile';
  String get medium => 'Moyen';
  String get hard => 'Difficile';
  String get loading => 'Chargement des questions...';
  String get loadingError =>
      'Échec du chargement des questions. Veuillez réessayer.';
  String get question => 'Question';
  String get score => 'Score';
  String get seconds => 'secondes';
  String get yourScore => 'Votre score';
  String get excellent =>
      'Excellent ! Vous avez d\'excellentes connaissances !';
  String get good => 'Bon travail ! Vous avez une bonne compréhension.';
  String get average => 'Pas mal, mais il y a place à l\'amélioration.';
  String get poor =>
      'Continuez à vous entraîner pour améliorer vos connaissances.';
  String get questionReview => 'Révision des questions';
  String get yourAnswer => 'Votre réponse';
  String get correctAnswer => 'Réponse correcte';
  String get home => 'Accueil';
  String get newQuiz => 'Nouveau quiz';
  String get settings => 'Paramètres';
  String get theme => 'Thème';
  String get lightMode => 'Mode clair';
  String get darkMode => 'Mode sombre';
  String get language => 'Langue';
  String get sound => 'Son';
  String get vibration => 'Vibration';
  String get notifications => 'Notifications';
  String get on => 'Activé';
  String get off => 'Désactivé';
  String get leaderboard => 'Classement';
  String get resetScores => 'Réinitialiser les scores';
  String get resetScoresConfirm =>
      'Êtes-vous sûr de vouloir réinitialiser tous les scores ?';
  String get yes => 'Oui';
  String get no => 'Non';
  String get allCategories => 'Toutes les catégories';
  String get allDifficulties => 'Toutes les difficultés';
  String get date => 'Date';
  String get category => 'Catégorie';
  String get difficulty => 'Difficulté';
  String get noScores => 'Aucun score enregistré';
  String get bestScore => 'Meilleur score';
  String get vibrationNotSupported =>
      'Vibration non supportée sur cet appareil';
  String get excellentFeedback =>
      'Excellent ! Vous avez de très bonnes connaissances !';
  String get goodFeedback => 'Bon travail ! Vous avez une bonne compréhension.';
  String get okayFeedback => 'Pas mal, mais il y a place à l\'amélioration.';
  String get poorFeedback =>
      'Continuez à vous entraîner pour améliorer vos connaissances.';
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['fr', 'en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => true; // Changé à true pour forcer le rechargement
}
