import 'package:flutter/material.dart';

// Classe temporaire pour résoudre les erreurs d'importation
// Cette classe sera remplacée par la classe générée par Flutter
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations) ??
        AppLocalizations(const Locale('fr'));
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // Méthode pour obtenir les traductions selon la langue
  Map<String, String> get _localizedValues {
    switch (locale.languageCode) {
      case 'en':
        return _englishValues;
      case 'ar':
        return _arabicValues;
      case 'fr':
      default:
        return _frenchValues;
    }
  }

  // Traductions françaises
  static const Map<String, String> _frenchValues = {
    'appTitle': 'Quiz App',
    'welcomeMessage': 'Bienvenue sur Quiz App !',
    'welcomeDescription':
        'Testez vos connaissances avec des quiz de différentes catégories',
    'startQuiz': 'Commencer un quiz',
    'about': 'À propos',
    'aboutTitle': 'À propos de Quiz App',
    'aboutDescription':
        'Quiz App est une application de quiz interactive qui vous permet de tester vos connaissances dans diverses catégories.',
    'aboutApi':
        'Les questions sont récupérées depuis l\'API Open Trivia Database (OpenTDB).',
    'aboutFeatures': 'Fonctionnalités :',
    'aboutFeature1': '• Plusieurs catégories',
    'aboutFeature2': '• Différents niveaux de difficulté',
    'aboutFeature3': '• Suivi des scores',
    'aboutFeature4': '• Retour immédiat',
    'close': 'Fermer',
    'quizSettings': 'Paramètres du quiz',
    'selectCategory': 'Sélectionner une catégorie :',
    'selectDifficulty': 'Sélectionner la difficulté :',
    'numberOfQuestions': 'Nombre de questions :',
    'easy': 'Facile',
    'medium': 'Moyen',
    'hard': 'Difficile',
    'loading': 'Chargement des questions...',
    'loadingError': 'Échec du chargement des questions. Veuillez réessayer.',
    'question': 'Question',
    'score': 'Score',
    'seconds': 'secondes',
    'yourScore': 'Votre score',
    'excellent': 'Excellent ! Vous avez d\'excellentes connaissances !',
    'good': 'Bon travail ! Vous avez une bonne compréhension.',
    'average': 'Pas mal, mais il y a place à l\'amélioration.',
    'poor': 'Continuez à vous entraîner pour améliorer vos connaissances.',
    'questionReview': 'Révision des questions',
    'yourAnswer': 'Votre réponse',
    'correctAnswer': 'Réponse correcte',
    'home': 'Accueil',
    'newQuiz': 'Nouveau quiz',
    'settings': 'Paramètres',
    'theme': 'Thème',
    'lightMode': 'Mode clair',
    'darkMode': 'Mode sombre',
    'language': 'Langue',
    'sound': 'Son',
    'vibration': 'Vibration',
    'notifications': 'Notifications',
    'on': 'Activé',
    'off': 'Désactivé',
    'leaderboard': 'Classement',
    'resetScores': 'Réinitialiser les scores',
    'resetScoresConfirm':
        'Êtes-vous sûr de vouloir réinitialiser tous les scores ?',
    'yes': 'Oui',
    'no': 'Non',
    'allCategories': 'Toutes les catégories',
    'allDifficulties': 'Toutes les difficultés',
    'date': 'Date',
    'category': 'Catégorie',
    'difficulty': 'Difficulté',
    'noScores': 'Aucun score enregistré',
    'bestScore': 'Meilleur score',
    'vibrationNotSupported': 'Vibration non supportée sur cet appareil',
    'excellentFeedback': 'Excellent ! Vous avez de très bonnes connaissances !',
    'goodFeedback': 'Bon travail ! Vous avez une bonne compréhension.',
    'okayFeedback': 'Pas mal, mais il y a place à l\'amélioration.',
    'poorFeedback':
        'Continuez à vous entraîner pour améliorer vos connaissances.',
  };

  // Traductions anglaises
  static const Map<String, String> _englishValues = {
    'appTitle': 'Quiz App',
    'welcomeMessage': 'Welcome to Quiz App!',
    'welcomeDescription':
        'Test your knowledge with quizzes from different categories',
    'startQuiz': 'Start Quiz',
    'about': 'About',
    'aboutTitle': 'About Quiz App',
    'aboutDescription':
        'Quiz App is an interactive quiz application that allows you to test your knowledge in various categories.',
    'aboutApi':
        'Questions are retrieved from the Open Trivia Database (OpenTDB) API.',
    'aboutFeatures': 'Features:',
    'aboutFeature1': '• Multiple categories',
    'aboutFeature2': '• Different difficulty levels',
    'aboutFeature3': '• Score tracking',
    'aboutFeature4': '• Immediate feedback',
    'close': 'Close',
    'quizSettings': 'Quiz Settings',
    'selectCategory': 'Select a category:',
    'selectDifficulty': 'Select difficulty:',
    'numberOfQuestions': 'Number of questions:',
    'easy': 'Easy',
    'medium': 'Medium',
    'hard': 'Hard',
    'loading': 'Loading questions...',
    'loadingError': 'Failed to load questions. Please try again.',
    'question': 'Question',
    'score': 'Score',
    'seconds': 'seconds',
    'yourScore': 'Your Score',
    'excellent': 'Excellent! You have excellent knowledge!',
    'good': 'Good job! You have a good understanding.',
    'average': 'Not bad, but there\'s room for improvement.',
    'poor': 'Keep practicing to improve your knowledge.',
    'questionReview': 'Question Review',
    'yourAnswer': 'Your Answer',
    'correctAnswer': 'Correct Answer',
    'home': 'Home',
    'newQuiz': 'New Quiz',
    'settings': 'Settings',
    'theme': 'Theme',
    'lightMode': 'Light Mode',
    'darkMode': 'Dark Mode',
    'language': 'Language',
    'sound': 'Sound',
    'vibration': 'Vibration',
    'notifications': 'Notifications',
    'on': 'On',
    'off': 'Off',
    'leaderboard': 'Leaderboard',
    'resetScores': 'Reset Scores',
    'resetScoresConfirm': 'Are you sure you want to reset all scores?',
    'yes': 'Yes',
    'no': 'No',
    'allCategories': 'All Categories',
    'allDifficulties': 'All Difficulties',
    'date': 'Date',
    'category': 'Category',
    'difficulty': 'Difficulty',
    'noScores': 'No scores recorded',
    'bestScore': 'Best Score',
    'vibrationNotSupported': 'Vibration not supported on this device',
    'excellentFeedback': 'Excellent! You have very good knowledge!',
    'goodFeedback': 'Good job! You have a good understanding.',
    'okayFeedback': 'Not bad, but there\'s room for improvement.',
    'poorFeedback': 'Keep practicing to improve your knowledge.',
  };

  // Traductions arabes
  static const Map<String, String> _arabicValues = {
    'appTitle': 'تطبيق الاختبار',
    'welcomeMessage': 'مرحبا بك في تطبيق الاختبار!',
    'welcomeDescription': 'اختبر معرفتك بالاختبارات من فئات مختلفة',
    'startQuiz': 'ابدأ الاختبار',
    'about': 'حول',
    'aboutTitle': 'حول تطبيق الاختبار',
    'aboutDescription':
        'تطبيق الاختبار هو تطبيق اختبار تفاعلي يتيح لك اختبار معرفتك في فئات مختلفة.',
    'aboutApi':
        'يتم استرداد الأسئلة من واجهة برمجة تطبيقات قاعدة بيانات الأسئلة المفتوحة (OpenTDB).',
    'aboutFeatures': 'الميزات:',
    'aboutFeature1': '• فئات متعددة',
    'aboutFeature2': '• مستويات صعوبة مختلفة',
    'aboutFeature3': '• تتبع النتائج',
    'aboutFeature4': '• ردود فعل فورية',
    'close': 'إغلاق',
    'quizSettings': 'إعدادات الاختبار',
    'selectCategory': 'اختر فئة:',
    'selectDifficulty': 'اختر الصعوبة:',
    'numberOfQuestions': 'عدد الأسئلة:',
    'easy': 'سهل',
    'medium': 'متوسط',
    'hard': 'صعب',
    'loading': 'جاري تحميل الأسئلة...',
    'loadingError': 'فشل في تحميل الأسئلة. يرجى المحاولة مرة أخرى.',
    'question': 'سؤال',
    'score': 'النتيجة',
    'seconds': 'ثانية',
    'yourScore': 'نتيجتك',
    'excellent': 'ممتاز! لديك معرفة ممتازة!',
    'good': 'عمل جيد! لديك فهم جيد.',
    'average': 'ليس سيئا، ولكن هناك مجال للتحسين.',
    'poor': 'استمر في الممارسة لتحسين معرفتك.',
    'questionReview': 'مراجعة الأسئلة',
    'yourAnswer': 'إجابتك',
    'correctAnswer': 'الإجابة الصحيحة',
    'home': 'الرئيسية',
    'newQuiz': 'اختبار جديد',
    'settings': 'الإعدادات',
    'theme': 'المظهر',
    'lightMode': 'الوضع الفاتح',
    'darkMode': 'الوضع الداكن',
    'language': 'اللغة',
    'sound': 'الصوت',
    'vibration': 'الاهتزاز',
    'notifications': 'الإشعارات',
    'on': 'مفعل',
    'off': 'معطل',
    'leaderboard': 'لوحة المتصدرين',
    'resetScores': 'إعادة تعيين النتائج',
    'resetScoresConfirm': 'هل أنت متأكد من أنك تريد إعادة تعيين جميع النتائج؟',
    'yes': 'نعم',
    'no': 'لا',
    'allCategories': 'جميع الفئات',
    'allDifficulties': 'جميع المستويات',
    'date': 'التاريخ',
    'category': 'الفئة',
    'difficulty': 'الصعوبة',
    'noScores': 'لا توجد نتائج مسجلة',
    'bestScore': 'أفضل نتيجة',
    'vibrationNotSupported': 'الاهتزاز غير مدعوم على هذا الجهاز',
    'excellentFeedback': 'ممتاز! لديك معرفة جيدة جدا!',
    'goodFeedback': 'عمل جيد! لديك فهم جيد.',
    'okayFeedback': 'ليس سيئا، ولكن هناك مجال للتحسين.',
    'poorFeedback': 'استمر في الممارسة لتحسين معرفتك.',
  };

  // Getters dynamiques pour les traductions
  String get appTitle => _localizedValues['appTitle']!;
  String get welcomeMessage => _localizedValues['welcomeMessage']!;
  String get welcomeDescription => _localizedValues['welcomeDescription']!;
  String get startQuiz => _localizedValues['startQuiz']!;
  String get about => _localizedValues['about']!;
  String get aboutTitle => _localizedValues['aboutTitle']!;
  String get aboutDescription => _localizedValues['aboutDescription']!;
  String get aboutApi => _localizedValues['aboutApi']!;
  String get aboutFeatures => _localizedValues['aboutFeatures']!;
  String get aboutFeature1 => _localizedValues['aboutFeature1']!;
  String get aboutFeature2 => _localizedValues['aboutFeature2']!;
  String get aboutFeature3 => _localizedValues['aboutFeature3']!;
  String get aboutFeature4 => _localizedValues['aboutFeature4']!;
  String get close => _localizedValues['close']!;
  String get quizSettings => _localizedValues['quizSettings']!;
  String get selectCategory => _localizedValues['selectCategory']!;
  String get selectDifficulty => _localizedValues['selectDifficulty']!;
  String get numberOfQuestions => _localizedValues['numberOfQuestions']!;
  String get easy => _localizedValues['easy']!;
  String get medium => _localizedValues['medium']!;
  String get hard => _localizedValues['hard']!;
  String get loading => _localizedValues['loading']!;
  String get loadingError => _localizedValues['loadingError']!;
  String get question => _localizedValues['question']!;
  String get score => _localizedValues['score']!;
  String get seconds => _localizedValues['seconds']!;
  String get yourScore => _localizedValues['yourScore']!;
  String get excellent => _localizedValues['excellent']!;
  String get good => _localizedValues['good']!;
  String get average => _localizedValues['average']!;
  String get poor => _localizedValues['poor']!;
  String get questionReview => _localizedValues['questionReview']!;
  String get yourAnswer => _localizedValues['yourAnswer']!;
  String get correctAnswer => _localizedValues['correctAnswer']!;
  String get home => _localizedValues['home']!;
  String get newQuiz => _localizedValues['newQuiz']!;
  String get settings => _localizedValues['settings']!;
  String get theme => _localizedValues['theme']!;
  String get lightMode => _localizedValues['lightMode']!;
  String get darkMode => _localizedValues['darkMode']!;
  String get language => _localizedValues['language']!;
  String get sound => _localizedValues['sound']!;
  String get vibration => _localizedValues['vibration']!;
  String get notifications => _localizedValues['notifications']!;
  String get on => _localizedValues['on']!;
  String get off => _localizedValues['off']!;
  String get leaderboard => _localizedValues['leaderboard']!;
  String get resetScores => _localizedValues['resetScores']!;
  String get resetScoresConfirm => _localizedValues['resetScoresConfirm']!;
  String get yes => _localizedValues['yes']!;
  String get no => _localizedValues['no']!;
  String get allCategories => _localizedValues['allCategories']!;
  String get allDifficulties => _localizedValues['allDifficulties']!;
  String get date => _localizedValues['date']!;
  String get category => _localizedValues['category']!;
  String get difficulty => _localizedValues['difficulty']!;
  String get noScores => _localizedValues['noScores']!;
  String get bestScore => _localizedValues['bestScore']!;
  String get vibrationNotSupported =>
      _localizedValues['vibrationNotSupported']!;
  String get excellentFeedback => _localizedValues['excellentFeedback']!;
  String get goodFeedback => _localizedValues['goodFeedback']!;
  String get okayFeedback => _localizedValues['okayFeedback']!;
  String get poorFeedback => _localizedValues['poorFeedback']!;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['fr', 'en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => true; // Changé à true pour forcer le rechargement
}
