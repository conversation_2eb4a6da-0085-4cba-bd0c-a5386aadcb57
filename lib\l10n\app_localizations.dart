import 'package:flutter/material.dart';

// Classe temporaire pour résoudre les erreurs d'importation
// Cette classe sera remplacée par la classe générée par Flutter
class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations) ??
        AppLocalizations(const Locale('fr'));
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // Méthode pour obtenir les traductions selon la langue
  Map<String, String> get _localizedValues {
    switch (locale.languageCode) {
      case 'en':
        return _englishValues;
      case 'ar':
        return _arabicValues;
      case 'fr':
      default:
        return _frenchValues;
    }
  }

  // Traductions françaises - Essentielles pour l'application de quiz
  static const Map<String, String> _frenchValues = {
    'appTitle': 'Quiz App',
    'welcomeMessage': 'Bienvenue sur Quiz App !',
    'welcomeDescription': 'Testez vos connaissances avec des quiz',
    'startQuiz': 'Commencer un quiz',
    'about': 'À propos',
    'close': 'Fermer',
    'quizSettings': 'Paramètres du quiz',
    'selectCategory': 'Sélectionner une catégorie :',
    'selectDifficulty': 'Sélectionner la difficulté :',
    'numberOfQuestions': 'Nombre de questions :',
    'easy': 'Facile',
    'medium': 'Moyen',
    'hard': 'Difficile',
    'loading': 'Chargement des questions...',
    'loadingError': 'Échec du chargement. Réessayez.',
    'question': 'Question',
    'score': 'Score',
    'seconds': 'secondes',
    'yourScore': 'Votre score',
    'questionReview': 'Révision des questions',
    'yourAnswer': 'Votre réponse',
    'correctAnswer': 'Réponse correcte',
    'home': 'Accueil',
    'newQuiz': 'Nouveau quiz',
    'settings': 'Paramètres',
    'leaderboard': 'Classement',
    'language': 'Langue',
    'yes': 'Oui',
    'no': 'Non',
    'category': 'Catégorie',
    'difficulty': 'Difficulté',
    // Traductions supplémentaires utilisées dans l'app
    'aboutTitle': 'À propos de Quiz App',
    'aboutDescription': 'Quiz App est une application de quiz interactive.',
    'aboutApi': 'Questions récupérées depuis l\'API Open Trivia Database.',
    'aboutFeatures': 'Fonctionnalités :',
    'aboutFeature1': '• Plusieurs catégories',
    'aboutFeature2': '• Différents niveaux',
    'aboutFeature3': '• Suivi des scores',
    'aboutFeature4': '• Retour immédiat',
  };

  // Traductions anglaises - Essentielles pour l'application de quiz
  static const Map<String, String> _englishValues = {
    'appTitle': 'Quiz App',
    'welcomeMessage': 'Welcome to Quiz App!',
    'welcomeDescription': 'Test your knowledge with quizzes',
    'startQuiz': 'Start Quiz',
    'about': 'About',
    'close': 'Close',
    'quizSettings': 'Quiz Settings',
    'selectCategory': 'Select a category:',
    'selectDifficulty': 'Select difficulty:',
    'numberOfQuestions': 'Number of questions:',
    'easy': 'Easy',
    'medium': 'Medium',
    'hard': 'Hard',
    'loading': 'Loading questions...',
    'loadingError': 'Failed to load. Try again.',
    'question': 'Question',
    'score': 'Score',
    'seconds': 'seconds',
    'yourScore': 'Your Score',
    'questionReview': 'Question Review',
    'yourAnswer': 'Your Answer',
    'correctAnswer': 'Correct Answer',
    'home': 'Home',
    'newQuiz': 'New Quiz',
    'settings': 'Settings',
    'leaderboard': 'Leaderboard',
    'language': 'Language',
    'yes': 'Yes',
    'no': 'No',
    'category': 'Category',
    'difficulty': 'Difficulty',
    // Additional translations used in the app
    'aboutTitle': 'About Quiz App',
    'aboutDescription': 'Quiz App is an interactive quiz application.',
    'aboutApi': 'Questions retrieved from Open Trivia Database API.',
    'aboutFeatures': 'Features:',
    'aboutFeature1': '• Multiple categories',
    'aboutFeature2': '• Different levels',
    'aboutFeature3': '• Score tracking',
    'aboutFeature4': '• Immediate feedback',
  };

  // Traductions arabes - Essentielles pour l'application de quiz
  static const Map<String, String> _arabicValues = {
    'appTitle': 'تطبيق الاختبار',
    'welcomeMessage': 'مرحبا بك في تطبيق الاختبار!',
    'welcomeDescription': 'اختبر معرفتك بالاختبارات',
    'startQuiz': 'ابدأ الاختبار',
    'about': 'حول',
    'close': 'إغلاق',
    'quizSettings': 'إعدادات الاختبار',
    'selectCategory': 'اختر فئة:',
    'selectDifficulty': 'اختر الصعوبة:',
    'numberOfQuestions': 'عدد الأسئلة:',
    'easy': 'سهل',
    'medium': 'متوسط',
    'hard': 'صعب',
    'loading': 'جاري تحميل الأسئلة...',
    'loadingError': 'فشل في التحميل. حاول مرة أخرى.',
    'question': 'سؤال',
    'score': 'النتيجة',
    'seconds': 'ثانية',
    'yourScore': 'نتيجتك',
    'questionReview': 'مراجعة الأسئلة',
    'yourAnswer': 'إجابتك',
    'correctAnswer': 'الإجابة الصحيحة',
    'home': 'الرئيسية',
    'newQuiz': 'اختبار جديد',
    'settings': 'الإعدادات',
    'leaderboard': 'لوحة المتصدرين',
    'language': 'اللغة',
    'yes': 'نعم',
    'no': 'لا',
    'category': 'الفئة',
    'difficulty': 'الصعوبة',
    // ترجمات إضافية مستخدمة في التطبيق
    'aboutTitle': 'حول تطبيق الاختبار',
    'aboutDescription': 'تطبيق الاختبار هو تطبيق اختبار تفاعلي.',
    'aboutApi':
        'الأسئلة مسترجعة من واجهة برمجة تطبيقات قاعدة البيانات المفتوحة.',
    'aboutFeatures': 'الميزات:',
    'aboutFeature1': '• فئات متعددة',
    'aboutFeature2': '• مستويات مختلفة',
    'aboutFeature3': '• تتبع النتائج',
    'aboutFeature4': '• ردود فعل فورية',
  };

  // Getters essentiels pour l'application de quiz
  String get appTitle => _localizedValues['appTitle']!;
  String get welcomeMessage => _localizedValues['welcomeMessage']!;
  String get welcomeDescription => _localizedValues['welcomeDescription']!;
  String get startQuiz => _localizedValues['startQuiz']!;
  String get about => _localizedValues['about']!;
  String get close => _localizedValues['close']!;
  String get quizSettings => _localizedValues['quizSettings']!;
  String get selectCategory => _localizedValues['selectCategory']!;
  String get selectDifficulty => _localizedValues['selectDifficulty']!;
  String get numberOfQuestions => _localizedValues['numberOfQuestions']!;
  String get easy => _localizedValues['easy']!;
  String get medium => _localizedValues['medium']!;
  String get hard => _localizedValues['hard']!;
  String get loading => _localizedValues['loading']!;
  String get loadingError => _localizedValues['loadingError']!;
  String get question => _localizedValues['question']!;
  String get score => _localizedValues['score']!;
  String get seconds => _localizedValues['seconds']!;
  String get yourScore => _localizedValues['yourScore']!;
  String get questionReview => _localizedValues['questionReview']!;
  String get yourAnswer => _localizedValues['yourAnswer']!;
  String get correctAnswer => _localizedValues['correctAnswer']!;
  String get home => _localizedValues['home']!;
  String get newQuiz => _localizedValues['newQuiz']!;
  String get settings => _localizedValues['settings']!;
  String get leaderboard => _localizedValues['leaderboard']!;
  String get language => _localizedValues['language']!;
  String get yes => _localizedValues['yes']!;
  String get no => _localizedValues['no']!;
  String get category => _localizedValues['category']!;
  String get difficulty => _localizedValues['difficulty']!;
  // Getters supplémentaires utilisés dans l'app
  String get aboutTitle => _localizedValues['aboutTitle']!;
  String get aboutDescription => _localizedValues['aboutDescription']!;
  String get aboutApi => _localizedValues['aboutApi']!;
  String get aboutFeatures => _localizedValues['aboutFeatures']!;
  String get aboutFeature1 => _localizedValues['aboutFeature1']!;
  String get aboutFeature2 => _localizedValues['aboutFeature2']!;
  String get aboutFeature3 => _localizedValues['aboutFeature3']!;
  String get aboutFeature4 => _localizedValues['aboutFeature4']!;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['fr', 'en', 'ar'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => true; // Changé à true pour forcer le rechargement
}
