import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import '../services/audio_service.dart';
import '../services/vibration_service.dart';
import '../services/localization_service.dart';
import '../widgets/rtl_aware_widget.dart';
import '../widgets/simple_background.dart';

import 'quiz_settings_screen.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<QuizProvider>(context, listen: false).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final audioService = AudioService();
    final vibrationService = VibrationService();
    final l10n = AppLocalizations.of(context);

    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return SimpleBackground(
          backgroundColor: Colors.grey[50],
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child:
                localizationService.isChangingLanguage
                    ? Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    )
                    : SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(height: 40),

                              // Logo animé avec effet néon
                              TweenAnimationBuilder<double>(
                                duration: const Duration(milliseconds: 1200),
                                tween: Tween(begin: 0.0, end: 1.0),
                                curve: Curves.elasticOut,
                                builder: (context, value, child) {
                                  return Transform.scale(
                                    scale: value,
                                    child: Container(
                                      width: 120,
                                      height: 120,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: LinearGradient(
                                          colors: [
                                            Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                            Theme.of(
                                              context,
                                            ).colorScheme.secondary,
                                            Theme.of(
                                              context,
                                            ).colorScheme.tertiary,
                                          ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary
                                                .withValues(alpha: 0.4),
                                            blurRadius: 30,
                                            spreadRadius: 8,
                                          ),
                                          BoxShadow(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .secondary
                                                .withValues(alpha: 0.3),
                                            blurRadius: 20,
                                            spreadRadius: 4,
                                          ),
                                        ],
                                      ),
                                      child: const Icon(
                                        Icons.quiz,
                                        size: 60,
                                        color: Colors.white,
                                      ),
                                    ),
                                  );
                                },
                              ),

                              const SizedBox(height: 40),

                              // Titre principal
                              SimpleCard(
                                padding: const EdgeInsets.all(28),
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                ),
                                child: Column(
                                  children: [
                                    RTLAwareText(
                                      l10n.welcomeMessage,
                                      style: TextStyle(
                                        fontSize: 28,
                                        fontWeight: FontWeight.bold,
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 16),
                                    RTLAwareText(
                                      l10n.welcomeDescription,
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.7),
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 40),

                              // Bouton principal
                              SimpleButton(
                                text: l10n.startQuiz,
                                icon: Icons.play_arrow,
                                onPressed: () {
                                  vibrationService.vibrateOnButtonPress();
                                  audioService.playButtonSound();
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) =>
                                              const QuizSettingsScreen(),
                                    ),
                                  );
                                },
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 48,
                                  vertical: 16,
                                ),
                                color: Theme.of(context).colorScheme.primary,
                              ),

                              const SizedBox(height: 20),

                              // Bouton À propos
                              SimpleButton(
                                text: l10n.about,
                                icon: Icons.info_outline,
                                onPressed: () {
                                  vibrationService.vibrateOnButtonPress();
                                  audioService.playButtonSound();
                                  _showAboutDialog(context);
                                },
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 32,
                                  vertical: 12,
                                ),
                                color: Theme.of(context).colorScheme.secondary,
                                isSecondary: true,
                              ),

                              const SizedBox(height: 40),
                            ],
                          ),
                        ),
                      ),
                    ),
          ),
        );
      },
    );
  }

  void _showAboutDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer<LocalizationService>(
          builder: (context, localizationService, _) {
            return Directionality(
              textDirection: localizationService.textDirection,
              child: AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: RTLAwareText(
                  l10n.aboutTitle,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SimpleCard(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RTLAwareText(
                              l10n.aboutDescription,
                              style: TextStyle(
                                fontSize: 16,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 16),
                            RTLAwareText(
                              l10n.aboutApi,
                              style: TextStyle(
                                fontSize: 14,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      RTLAwareText(
                        l10n.aboutFeatures,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildFeatureItem(l10n.aboutFeature1, Icons.category),
                      _buildFeatureItem(l10n.aboutFeature2, Icons.tune),
                      _buildFeatureItem(l10n.aboutFeature3, Icons.score),
                      _buildFeatureItem(l10n.aboutFeature4, Icons.feedback),
                    ],
                  ),
                ),
                actions: [
                  SimpleButton(
                    text: l10n.close,
                    onPressed: () => Navigator.of(context).pop(),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 8,
                    ),
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFeatureItem(String text, IconData icon) {
    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              if (!localizationService.isRTL) ...[
                Icon(
                  icon,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
              ],
              Expanded(
                child: RTLAwareText(
                  text,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              if (localizationService.isRTL) ...[
                const SizedBox(width: 8),
                Icon(
                  icon,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
