import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/quiz_provider.dart';
import '../services/audio_service.dart';
import '../services/vibration_service.dart';
import 'quiz_settings_screen.dart';
import 'settings_screen.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize the quiz provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<QuizProvider>(context, listen: false).initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    final audioService = AudioService();
    final vibrationService = VibrationService();
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.appTitle),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              vibrationService.vibrateOnButtonPress();
              audioService.playButtonSound();
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
            tooltip: l10n.settings,
          ),
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.quiz, size: 100, color: Colors.blue),
              const SizedBox(height: 30),
              Text(
                l10n.welcomeMessage,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                l10n.welcomeDescription,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 40),
              ElevatedButton(
                onPressed: () {
                  vibrationService.vibrateOnButtonPress();
                  audioService.playButtonSound();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const QuizSettingsScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 40,
                    vertical: 15,
                  ),
                ),
                child: Text(
                  l10n.startQuiz,
                  style: const TextStyle(fontSize: 18),
                ),
              ),

              const SizedBox(height: 20),
              TextButton(
                onPressed: () {
                  vibrationService.vibrateOnButtonPress();
                  audioService.playButtonSound();
                  _showAboutDialog(context);
                },
                child: Text(l10n.about),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.aboutTitle),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(l10n.aboutDescription),
                const SizedBox(height: 10),
                Text(l10n.aboutApi),
                const SizedBox(height: 10),
                Text(l10n.aboutFeatures),
                Text(l10n.aboutFeature1),
                Text(l10n.aboutFeature2),
                Text(l10n.aboutFeature3),
                Text(l10n.aboutFeature4),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Text(l10n.close),
              ),
            ],
          ),
    );
  }
}
