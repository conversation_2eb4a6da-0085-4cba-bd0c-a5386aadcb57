import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/localization_service.dart';
import '../widgets/rtl_aware_widget.dart';
import '../widgets/language_switcher.dart';
import '../screens/welcome_screen.dart';
import '../widgets/leaderboard_screen.dart';
import '../screens/settings_screen.dart';

class MainLayout extends StatefulWidget {
  final Widget child;
  final String title;
  final int currentIndex;
  final bool showAppBar;
  final bool showBottomNav;
  final List<Widget>? actions;

  const MainLayout({
    super.key,
    required this.child,
    required this.title,
    this.currentIndex = 0,
    this.showAppBar = true,
    this.showBottomNav = true,
    this.actions,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  void _onBottomNavTap(int index) {
    switch (index) {
      case 0:
        // Accueil
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const WelcomeScreen()),
          (route) => false,
        );
        break;
      case 1:
        // Classement
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LeaderboardScreen()),
          (route) => false,
        );
        break;
      case 2:
        // Paramètres
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const SettingsScreen()),
          (route) => false,
        );
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return Directionality(
          textDirection: localizationService.textDirection,
          child: Scaffold(
            appBar:
                widget.showAppBar
                    ? AppBar(
                      title: RTLAwareText(
                        widget.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 22,
                          color: Colors.white,
                        ),
                      ),
                      centerTitle: true,
                      iconTheme: const IconThemeData(color: Colors.white),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      elevation: 0,
                      flexibleSpace: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Theme.of(context).colorScheme.primary,
                              Theme.of(context).colorScheme.secondary,
                            ],
                          ),
                        ),
                      ),
                      actions:
                          widget.actions ??
                          [
                            IconButton(
                              icon: const Icon(
                                Icons.notifications_outlined,
                                color: Colors.white,
                              ),
                              onPressed: () {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: RTLAwareText('Notifications'),
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                );
                              },
                            ),
                            const LanguageSwitcher(),
                            const SizedBox(width: 8),
                          ],
                    )
                    : null,
            body: widget.child,
            bottomNavigationBar:
                widget.showBottomNav
                    ? Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, -2),
                          ),
                        ],
                      ),
                      child: BottomNavigationBar(
                        currentIndex: widget.currentIndex,
                        onTap: _onBottomNavTap,
                        type: BottomNavigationBarType.fixed,
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        selectedItemColor:
                            Theme.of(context).colorScheme.primary,
                        unselectedItemColor: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.6),
                        selectedLabelStyle: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                        items: [
                          BottomNavigationBarItem(
                            icon: const Icon(Icons.home_outlined),
                            activeIcon: const Icon(Icons.home),
                            label: l10n.home,
                          ),
                          BottomNavigationBarItem(
                            icon: const Icon(Icons.leaderboard_outlined),
                            activeIcon: const Icon(Icons.leaderboard),
                            label: l10n.leaderboard,
                          ),
                          BottomNavigationBarItem(
                            icon: const Icon(Icons.settings_outlined),
                            activeIcon: const Icon(Icons.settings),
                            label: l10n.settings,
                          ),
                        ],
                      ),
                    )
                    : null,
          ),
        );
      },
    );
  }
}
