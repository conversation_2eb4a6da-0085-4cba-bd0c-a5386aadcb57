import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/localization_service.dart';
import '../services/score_service.dart';
import 'rtl_aware_widget.dart';

class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedCategory = 'all';
  String _selectedDifficulty = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final scoreService = Provider.of<ScoreService>(context);

    return Consumer<LocalizationService>(
      builder: (context, localizationService, _) {
        return Directionality(
          textDirection: localizationService.textDirection,
          child: Scaffold(
            body: Column(
              children: [
                // Filters section
                _buildFiltersSection(l10n, localizationService),

                // Tabs
                _buildTabBar(l10n, localizationService),

                // Content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAllScoresTab(
                        scoreService,
                        l10n,
                        localizationService,
                      ),
                      _buildBestScoresTab(
                        scoreService,
                        l10n,
                        localizationService,
                      ),
                      _buildStatsTab(scoreService, l10n, localizationService),
                    ],
                  ),
                ),
              ],
            ),
            floatingActionButton: _buildResetButton(scoreService, l10n),
          ),
        );
      },
    );
  }

  Widget _buildFiltersSection(
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    return AnimatedQuizCard(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RTLAwareText(
            'Filtres / Filters / المرشحات',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildFilterDropdown(
                  'Catégorie / Category / الفئة',
                  _selectedCategory,
                  ['all', 'Science', 'History', 'Sports'],
                  (value) => setState(() => _selectedCategory = value!),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFilterDropdown(
                  'Difficulté / Difficulty / الصعوبة',
                  _selectedDifficulty,
                  ['all', 'easy', 'medium', 'hard'],
                  (value) => setState(() => _selectedDifficulty = value!),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RTLAwareText(
          label,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButton<String>(
            value: value,
            isExpanded: true,
            underline: const SizedBox(),
            items:
                items.map((item) {
                  return DropdownMenuItem(
                    value: item,
                    child: RTLAwareText(
                      item == 'all' ? 'Tous / All / الكل' : item,
                    ),
                  );
                }).toList(),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar(
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Theme.of(context).colorScheme.primary,
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurface,
        labelStyle: const TextStyle(fontWeight: FontWeight.bold),
        tabs: [
          Tab(text: 'Tous / All / الكل'),
          Tab(text: 'Meilleurs / Best / الأفضل'),
          Tab(text: 'Stats'),
        ],
      ),
    );
  }

  Widget _buildAllScoresTab(
    ScoreService scoreService,
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    final scores = _getFilteredScores(scoreService.scores);

    if (scores.isEmpty) {
      return _buildEmptyState(l10n);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: scores.length,
      itemBuilder: (context, index) {
        final score = scores[index];
        return _buildScoreCard(score, index + 1, localizationService);
      },
    );
  }

  Widget _buildBestScoresTab(
    ScoreService scoreService,
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    final bestScores = _getBestScores(scoreService.scores);

    if (bestScores.isEmpty) {
      return _buildEmptyState(l10n);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bestScores.length,
      itemBuilder: (context, index) {
        final score = bestScores[index];
        return _buildScoreCard(
          score,
          index + 1,
          localizationService,
          isBest: true,
        );
      },
    );
  }

  Widget _buildStatsTab(
    ScoreService scoreService,
    AppLocalizations l10n,
    LocalizationService localizationService,
  ) {
    final scores = scoreService.scores;

    if (scores.isEmpty) {
      return _buildEmptyState(l10n);
    }

    final totalQuizzes = scores.length;
    final averageScore =
        scores.map((s) => s.percentage).reduce((a, b) => a + b) / totalQuizzes;
    final bestScore = scores
        .map((s) => s.percentage)
        .reduce((a, b) => a > b ? a : b);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildStatCard(
            'Total Quiz / إجمالي الاختبارات',
            totalQuizzes.toString(),
            Icons.quiz,
          ),
          _buildStatCard(
            'Moyenne / Average / المتوسط',
            '${averageScore.toStringAsFixed(1)}%',
            Icons.trending_up,
          ),
          _buildStatCard(
            'Meilleur / Best / الأفضل',
            '${bestScore.toStringAsFixed(1)}%',
            Icons.star,
          ),
        ],
      ),
    );
  }

  Widget _buildScoreCard(
    ScoreEntry score,
    int rank,
    LocalizationService localizationService, {
    bool isBest = false,
  }) {
    return AnimatedQuizCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Rank
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getRankColor(rank),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: RTLAwareText(
                rank.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Score info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RTLAwareText(
                  '${score.score}/${score.totalQuestions} (${score.percentage.toStringAsFixed(1)}%)',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                RTLAwareText(
                  '${score.category} - ${score.difficulty}',
                  style: TextStyle(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                RTLAwareText(
                  _formatDate(score.date),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),

          if (isBest) Icon(Icons.star, color: Colors.amber, size: 24),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return AnimatedQuizCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RTLAwareText(
                  title,
                  style: TextStyle(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                RTLAwareText(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.emoji_events_outlined,
            size: 64,
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          RTLAwareText(
            'Aucun score / No scores / لا توجد نتائج',
            style: TextStyle(
              fontSize: 18,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResetButton(ScoreService scoreService, AppLocalizations l10n) {
    return FloatingActionButton.extended(
      onPressed: () => _showResetDialog(scoreService, l10n),
      icon: const Icon(Icons.refresh),
      label: RTLAwareText('Reset'),
      backgroundColor: Colors.red,
      foregroundColor: Colors.white,
    );
  }

  void _showResetDialog(ScoreService scoreService, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: RTLAwareText('Confirmer / Confirm / تأكيد'),
            content: RTLAwareText(
              'Supprimer tous les scores? / Delete all scores? / حذف جميع النتائج؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: RTLAwareText('Annuler / Cancel / إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  scoreService.resetScores();
                  Navigator.pop(context);
                },
                child: RTLAwareText('Supprimer / Delete / حذف'),
              ),
            ],
          ),
    );
  }

  List<ScoreEntry> _getFilteredScores(List<ScoreEntry> scores) {
    return scores.where((score) {
      final categoryMatch =
          _selectedCategory == 'all' ||
          score.category.contains(_selectedCategory);
      final difficultyMatch =
          _selectedDifficulty == 'all' ||
          score.difficulty == _selectedDifficulty;
      return categoryMatch && difficultyMatch;
    }).toList();
  }

  List<ScoreEntry> _getBestScores(List<ScoreEntry> scores) {
    final filtered = _getFilteredScores(scores);
    filtered.sort((a, b) => b.percentage.compareTo(a.percentage));
    return filtered.take(10).toList();
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber;
      case 2:
        return Colors.grey[400]!;
      case 3:
        return Colors.brown[400]!;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
