import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import '../services/score_service.dart';

class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen> {
  final ScoreService _scoreService = ScoreService();
  String _selectedCategory = 'all';
  String _selectedDifficulty = 'all';
  List<ScoreEntry> _filteredScores = [];

  @override
  void initState() {
    super.initState();
    _filterScores();
  }

  void _filterScores() {
    setState(() {
      if (_selectedCategory == 'all' && _selectedDifficulty == 'all') {
        _filteredScores = List.from(_scoreService.scores);
      } else if (_selectedCategory == 'all') {
        _filteredScores = _scoreService.getScoresByDifficulty(
          _selectedDifficulty,
        );
      } else if (_selectedDifficulty == 'all') {
        _filteredScores = _scoreService.getScoresByCategory(_selectedCategory);
      } else {
        _filteredScores = _scoreService.getScoresByCategoryAndDifficulty(
          _selectedCategory,
          _selectedDifficulty,
        );
      }
    });
  }

  void _resetScores() async {
    final l10n = AppLocalizations.of(context);
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.resetScores),
            content: Text(l10n.resetScoresConfirm),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text(l10n.no),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(l10n.yes),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      await _scoreService.resetScores();
      setState(() {
        _filteredScores = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.leaderboard),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetScores,
            tooltip: l10n.resetScores,
          ),
        ],
      ),
      body: Column(
        children: [
          // Filters
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.category,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                _buildCategoryFilter(l10n),
                const SizedBox(height: 16),
                Text(
                  l10n.difficulty,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                _buildDifficultyFilter(l10n),
              ],
            ),
          ),

          // Best score card
          if (_filteredScores.isNotEmpty) _buildBestScoreCard(l10n),

          // Scores list
          Expanded(
            child:
                _filteredScores.isEmpty
                    ? Center(child: Text(l10n.noScores))
                    : ListView.builder(
                      itemCount: _filteredScores.length,
                      itemBuilder: (context, index) {
                        final score = _filteredScores[index];
                        return _buildScoreCard(score, l10n);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter(AppLocalizations l10n) {
    // Get unique categories from scores
    final categories =
        _scoreService.scores.map((score) => score.category).toSet().toList();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          FilterChip(
            label: Text(l10n.allCategories),
            selected: _selectedCategory == 'all',
            onSelected: (selected) {
              if (selected) {
                setState(() {
                  _selectedCategory = 'all';
                  _filterScores();
                });
              }
            },
          ),
          const SizedBox(width: 8),
          ...categories.map((category) {
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(category),
                selected: _selectedCategory == category,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedCategory = category;
                      _filterScores();
                    });
                  }
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDifficultyFilter(AppLocalizations l10n) {
    return Row(
      children: [
        FilterChip(
          label: Text(l10n.allDifficulties),
          selected: _selectedDifficulty == 'all',
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedDifficulty = 'all';
                _filterScores();
              });
            }
          },
        ),
        const SizedBox(width: 8),
        FilterChip(
          label: Text(l10n.easy),
          selected: _selectedDifficulty == 'easy',
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedDifficulty = 'easy';
                _filterScores();
              });
            }
          },
        ),
        const SizedBox(width: 8),
        FilterChip(
          label: Text(l10n.medium),
          selected: _selectedDifficulty == 'medium',
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedDifficulty = 'medium';
                _filterScores();
              });
            }
          },
        ),
        const SizedBox(width: 8),
        FilterChip(
          label: Text(l10n.hard),
          selected: _selectedDifficulty == 'hard',
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedDifficulty = 'hard';
                _filterScores();
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildBestScoreCard(AppLocalizations l10n) {
    // Find the best score in the filtered list
    final bestScore = _filteredScores.reduce(
      (a, b) => a.percentage > b.percentage ? a : b,
    );

    return Card(
      margin: const EdgeInsets.all(16),
      color: Colors.blue.shade100,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.emoji_events, color: Colors.amber, size: 32),
                const SizedBox(width: 8),
                Text(
                  l10n.bestScore,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('${l10n.category}: ${bestScore.category}'),
            Text('${l10n.difficulty}: ${bestScore.difficulty}'),
            Text(
              '${l10n.score}: ${bestScore.score}/${bestScore.totalQuestions} (${bestScore.percentage.toStringAsFixed(1)}%)',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              '${l10n.date}: ${DateFormat.yMMMd().format(bestScore.date)}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScoreCard(ScoreEntry score, AppLocalizations l10n) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        title: Text(score.category),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${l10n.difficulty}: ${score.difficulty}'),
            Text(
              '${l10n.date}: ${DateFormat.yMMMd().format(score.date)}',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${score.score}/${score.totalQuestions}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            Text(
              '${score.percentage.toStringAsFixed(1)}%',
              style: TextStyle(
                color: _getColorForPercentage(score.percentage),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getColorForPercentage(double percentage) {
    if (percentage >= 80) {
      return Colors.green;
    } else if (percentage >= 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
