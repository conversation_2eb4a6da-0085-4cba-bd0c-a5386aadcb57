class Question {
  final String question;
  final String correctAnswer;
  final List<String> incorrectAnswers;
  final String category;
  final String difficulty;
  final String type;

  // Versions traduites (optionnelles)
  String? translatedQuestion;
  String? translatedCorrectAnswer;
  List<String>? translatedIncorrectAnswers;
  String? translatedCategory;

  // Réponses mélangées une seule fois
  List<String>? _shuffledAnswers;
  List<String>? _shuffledDisplayAnswers;

  Question({
    required this.question,
    required this.correctAnswer,
    required this.incorrectAnswers,
    required this.category,
    required this.difficulty,
    required this.type,
    this.translatedQuestion,
    this.translatedCorrectAnswer,
    this.translatedIncorrectAnswers,
    this.translatedCategory,
  });

  List<String> get allAnswers {
    if (_shuffledAnswers == null) {
      final answers = List<String>.from(incorrectAnswers);
      answers.add(correctAnswer);
      answers.shuffle();
      _shuffledAnswers = answers;
    }
    return _shuffledAnswers!;
  }

  // Getters pour les versions traduites ou originales
  String get displayQuestion => translatedQuestion ?? question;
  String get displayCorrectAnswer => translatedCorrectAnswer ?? correctAnswer;
  List<String> get displayIncorrectAnswers =>
      translatedIncorrectAnswers ?? incorrectAnswers;
  String get displayCategory => translatedCategory ?? category;

  List<String> get displayAllAnswers {
    if (_shuffledDisplayAnswers == null) {
      final answers = List<String>.from(displayIncorrectAnswers);
      answers.add(displayCorrectAnswer);
      answers.shuffle();
      _shuffledDisplayAnswers = answers;
    }
    return _shuffledDisplayAnswers!;
  }

  // Méthode pour définir les traductions
  void setTranslations({
    String? question,
    String? correctAnswer,
    List<String>? incorrectAnswers,
    String? category,
  }) {
    translatedQuestion = question;
    translatedCorrectAnswer = correctAnswer;
    translatedIncorrectAnswers = incorrectAnswers;
    translatedCategory = category;

    // Réinitialiser les réponses mélangées pour qu'elles soient recalculées avec les traductions
    _shuffledDisplayAnswers = null;
  }

  // Méthode pour effacer les traductions
  void clearTranslations() {
    translatedQuestion = null;
    translatedCorrectAnswer = null;
    translatedIncorrectAnswers = null;
    translatedCategory = null;

    // Réinitialiser les réponses mélangées
    _shuffledAnswers = null;
    _shuffledDisplayAnswers = null;
  }

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      question: json['question'],
      correctAnswer: json['correct_answer'],
      incorrectAnswers: List<String>.from(json['incorrect_answers']),
      category: json['category'],
      difficulty: json['difficulty'],
      type: json['type'],
    );
  }
}
