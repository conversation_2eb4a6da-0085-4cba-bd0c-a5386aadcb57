import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  bool _areNotificationsEnabled = true;

  Future<void> initialize() async {
    if (_isInitialized) return;

    tz_data.initializeTimeZones();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
      },
    );

    // Load notification preferences
    final prefs = await SharedPreferences.getInstance();
    _areNotificationsEnabled = prefs.getBool('notifications_enabled') ?? true;

    _isInitialized = true;
  }

  Future<void> scheduleReminderNotification() async {
    if (!_isInitialized || !_areNotificationsEnabled) return;

    // Cancel any existing reminder notifications
    await flutterLocalNotificationsPlugin.cancelAll();

    // Schedule a daily reminder at 6:00 PM
    final now = DateTime.now();
    final scheduledDate = DateTime(
      now.year,
      now.month,
      now.day,
      18, // 6:00 PM
      0,
    );

    // If the time has already passed today, schedule for tomorrow
    final effectiveDate = scheduledDate.isBefore(now)
        ? scheduledDate.add(const Duration(days: 1))
        : scheduledDate;

    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'reminder_channel',
      'Daily Reminders',
      channelDescription: 'Notifications to remind you to play quiz',
      importance: Importance.high,
      priority: Priority.high,
    );

    const NotificationDetails notificationDetails =
        NotificationDetails(android: androidDetails);

    await flutterLocalNotificationsPlugin.zonedSchedule(
      0,
      'Quiz Time!',
      'Test your knowledge with a quick quiz!',
      tz.TZDateTime.from(effectiveDate, tz.local),
      notificationDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }

  Future<void> showNewContentNotification(String title, String body) async {
    if (!_isInitialized || !_areNotificationsEnabled) return;

    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'new_content_channel',
      'New Content',
      channelDescription: 'Notifications about new content',
      importance: Importance.high,
      priority: Priority.high,
    );

    const NotificationDetails notificationDetails =
        NotificationDetails(android: androidDetails);

    await flutterLocalNotificationsPlugin.show(
      1, // Different ID from reminder
      title,
      body,
      notificationDetails,
    );
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    _areNotificationsEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', enabled);

    if (enabled) {
      await scheduleReminderNotification();
    } else {
      await flutterLocalNotificationsPlugin.cancelAll();
    }
  }

  bool get areNotificationsEnabled => _areNotificationsEnabled;
}
