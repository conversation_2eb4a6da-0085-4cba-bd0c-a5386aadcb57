// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:timezone/timezone.dart' as tz;
// import 'package:timezone/data/latest.dart' as tz_data;

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
  //     FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  bool _areNotificationsEnabled = true;

  Future<void> initialize() async {
    if (_isInitialized) return;

    // Simplified initialization for web compatibility
    debugPrint('Initializing notification service (simplified for web)');

    // Load notification preferences
    final prefs = await SharedPreferences.getInstance();
    _areNotificationsEnabled = prefs.getBool('notifications_enabled') ?? true;

    _isInitialized = true;
  }

  Future<void> scheduleReminderNotification() async {
    if (!_isInitialized || !_areNotificationsEnabled) return;
    debugPrint('Scheduling reminder notification (simplified for web)');
  }

  Future<void> showNewContentNotification(String title, String body) async {
    if (!_isInitialized || !_areNotificationsEnabled) return;
    debugPrint('Showing notification: $title - $body (simplified for web)');
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    _areNotificationsEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', enabled);
    debugPrint('Notifications ${enabled ? 'enabled' : 'disabled'}');
  }

  bool get areNotificationsEnabled => _areNotificationsEnabled;
}
