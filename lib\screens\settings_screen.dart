import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';
import '../services/theme_provider.dart';
import '../services/audio_service.dart';
import '../services/vibration_service.dart';
import '../services/notification_service.dart';
import '../services/localization_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late AudioService _audioService;
  late VibrationService _vibrationService;
  late NotificationService _notificationService;
  late LocalizationService _localizationService;

  @override
  void initState() {
    super.initState();
    _audioService = AudioService();
    _vibrationService = VibrationService();
    _notificationService = NotificationService();
    _localizationService = LocalizationService();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(l10n.settings)),
      body: ListView(
        children: [
          // Theme section
          _buildSectionHeader(l10n.theme),
          _buildThemeSelector(themeProvider, l10n),

          const Divider(),

          // Language section
          _buildSectionHeader(l10n.language),
          _buildLanguageSelector(l10n),

          const Divider(),

          // Sound section
          _buildSectionHeader(l10n.sound),
          SwitchListTile(
            title: Text(_audioService.isSoundEnabled ? l10n.on : l10n.off),
            value: _audioService.isSoundEnabled,
            onChanged: (value) {
              setState(() {
                _audioService.setSoundEnabled(value);
              });
            },
          ),

          const Divider(),

          // Vibration section
          _buildSectionHeader(l10n.vibration),
          SwitchListTile(
            title: Text(
              _vibrationService.isVibrationEnabled ? l10n.on : l10n.off,
            ),
            value: _vibrationService.isVibrationEnabled,
            onChanged:
                _vibrationService.hasVibrator
                    ? (value) {
                      setState(() {
                        _vibrationService.setVibrationEnabled(value);
                      });
                    }
                    : null,
            subtitle:
                !_vibrationService.hasVibrator
                    ? const Text('Vibration not supported on this device')
                    : null,
          ),

          const Divider(),

          // Notifications section
          _buildSectionHeader(l10n.notifications),
          SwitchListTile(
            title: Text(
              _notificationService.areNotificationsEnabled ? l10n.on : l10n.off,
            ),
            value: _notificationService.areNotificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationService.setNotificationsEnabled(value);
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildThemeSelector(
    ThemeProvider themeProvider,
    AppLocalizations l10n,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.light_mode),
              label: Text(l10n.lightMode),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    !themeProvider.isDarkMode
                        ? Theme.of(context).colorScheme.primary
                        : null,
                foregroundColor:
                    !themeProvider.isDarkMode ? Colors.white : null,
              ),
              onPressed:
                  themeProvider.isDarkMode
                      ? () => themeProvider.toggleTheme()
                      : null,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              icon: const Icon(Icons.dark_mode),
              label: Text(l10n.darkMode),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    themeProvider.isDarkMode
                        ? Theme.of(context).colorScheme.primary
                        : null,
                foregroundColor: themeProvider.isDarkMode ? Colors.white : null,
              ),
              onPressed:
                  !themeProvider.isDarkMode
                      ? () => themeProvider.toggleTheme()
                      : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector(AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8,
            children:
                _localizationService.supportedLocales.map((locale) {
                  final isSelected =
                      _localizationService.locale.languageCode ==
                      locale.languageCode;
                  return ChoiceChip(
                    label: Text(
                      _localizationService.getLanguageName(locale.languageCode),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _localizationService.setLocale(locale);
                        });
                      }
                    },
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }
}
